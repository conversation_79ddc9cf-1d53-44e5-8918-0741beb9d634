"""
Celery application configuration
"""

import os
from celery import Celery
from kombu import Queue

from app.core.config import settings

# Create Celery instance
celery_app = Celery(
    "prosecurity_monitor",
    broker=getattr(settings, 'CELERY_BROKER_URL', 'redis://localhost:6379/0'),
    backend=getattr(settings, 'CELERY_RESULT_BACKEND', 'redis://localhost:6379/0'),
    include=[
        'app.tasks.scan_tasks',
        'app.tasks.threat_tasks',
        'app.tasks.notification_tasks',
        'app.tasks.maintenance_tasks'
    ]
)

# Celery configuration
celery_app.conf.update(
    # Task routing
    task_routes={
        'app.tasks.scan_tasks.*': {'queue': 'scans'},
        'app.tasks.threat_tasks.*': {'queue': 'threats'},
        'app.tasks.notification_tasks.*': {'queue': 'notifications'},
        'app.tasks.maintenance_tasks.*': {'queue': 'maintenance'}
    },
    
    # Queue configuration
    task_default_queue='default',
    task_queues=(
        Queue('default', routing_key='default'),
        Queue('scans', routing_key='scans'),
        Queue('threats', routing_key='threats'),
        Queue('notifications', routing_key='notifications'),
        Queue('maintenance', routing_key='maintenance'),
        Queue('priority', routing_key='priority')
    ),
    
    # Task execution settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Task time limits
    task_soft_time_limit=300,  # 5 minutes
    task_time_limit=600,       # 10 minutes
    
    # Worker settings
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_persistent=True,
    
    # Task retry settings
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    
    # Monitoring
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # Beat schedule for periodic tasks
    beat_schedule={
        'cleanup-old-data': {
            'task': 'app.tasks.maintenance_tasks.cleanup_old_data_task',
            'schedule': 86400.0,  # Daily
        },
        'update-threat-intelligence': {
            'task': 'app.tasks.threat_tasks.update_threat_intelligence_task',
            'schedule': 3600.0,  # Hourly
        },
        'train-ml-models': {
            'task': 'app.tasks.threat_tasks.train_ml_model_task',
            'schedule': 604800.0,  # Weekly
        },
        'send-weekly-reports': {
            'task': 'app.tasks.notification_tasks.send_weekly_report_task',
            'schedule': 604800.0,  # Weekly
        },
        'update-geolocation-data': {
            'task': 'app.tasks.maintenance_tasks.update_geolocation_data_task',
            'schedule': 2592000.0,  # Monthly
        }
    }
)

# Auto-discover tasks
celery_app.autodiscover_tasks()


@celery_app.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery setup"""
    print(f'Request: {self.request!r}')
    return 'Debug task completed'


# Task failure handler
@celery_app.task(bind=True)
def task_failure_handler(self, task_id, error, traceback):
    """Handle task failures"""
    print(f'Task {task_id} failed: {error}')
    # Here you could send notifications, log to external service, etc.


# Configure error handling
celery_app.conf.task_annotations = {
    '*': {
        'on_failure': task_failure_handler,
    }
}
