"""
Feature extraction for ML threat detection
"""

import re
import math
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urlparse, parse_qs
from pydantic import BaseModel
import numpy as np


class RequestFeatures(BaseModel):
    """Extracted features from HTTP request"""
    
    # URL features
    url_length: int = 0
    url_depth: int = 0
    url_params_count: int = 0
    url_suspicious_chars: int = 0
    url_entropy: float = 0.0
    has_suspicious_extensions: bool = False
    
    # Query parameter features
    param_count: int = 0
    param_total_length: int = 0
    param_max_length: int = 0
    param_avg_length: float = 0.0
    param_entropy: float = 0.0
    has_sql_keywords: bool = False
    has_script_tags: bool = False
    has_encoded_chars: bool = False
    
    # Header features
    user_agent_length: int = 0
    user_agent_entropy: float = 0.0
    is_bot_user_agent: bool = False
    missing_common_headers: int = 0
    suspicious_headers: int = 0
    
    # Body features
    body_length: int = 0
    body_entropy: float = 0.0
    has_binary_content: bool = False
    
    # Request pattern features
    method_risk_score: float = 0.0
    content_type_risk: float = 0.0
    
    # Behavioral features
    request_frequency: float = 0.0
    unique_params_ratio: float = 0.0
    error_rate: float = 0.0
    
    # Geographic features
    is_tor: bool = False
    is_vpn: bool = False
    is_proxy: bool = False
    country_risk_score: float = 0.0
    
    # Time-based features
    hour_of_day: int = 0
    day_of_week: int = 0
    is_weekend: bool = False
    is_night_time: bool = False


class FeatureExtractor:
    """Extract features from HTTP requests for ML models"""
    
    def __init__(self):
        self.sql_keywords = {
            'select', 'insert', 'update', 'delete', 'drop', 'create', 'alter',
            'union', 'where', 'from', 'into', 'values', 'table', 'database',
            'schema', 'exec', 'execute', 'sp_', 'xp_', 'cmdshell'
        }
        
        self.script_patterns = [
            r'<script[^>]*>',
            r'javascript:',
            r'on\w+\s*=',
            r'<iframe',
            r'<object',
            r'<embed',
            r'eval\s*\(',
            r'document\.',
            r'window\.',
            r'alert\s*\('
        ]
        
        self.suspicious_extensions = {
            '.php', '.asp', '.aspx', '.jsp', '.cgi', '.pl', '.py', '.rb',
            '.exe', '.bat', '.cmd', '.sh', '.ps1'
        }
        
        self.bot_patterns = [
            r'bot', r'crawler', r'spider', r'scraper', r'curl', r'wget',
            r'python', r'java', r'go-http', r'libwww', r'lwp'
        ]
        
        self.high_risk_countries = {
            'CN', 'RU', 'KP', 'IR', 'PK', 'BD', 'VN', 'ID'
        }
        
        self.common_headers = {
            'accept', 'accept-language', 'accept-encoding', 'user-agent',
            'host', 'connection', 'cache-control'
        }
    
    def extract_features(
        self,
        request_data: Dict[str, Any],
        behavioral_data: Optional[Dict[str, Any]] = None
    ) -> RequestFeatures:
        """Extract all features from request data"""
        
        url = request_data.get('url', '')
        method = request_data.get('method', 'GET')
        headers = request_data.get('headers', {})
        body = request_data.get('body', '')
        query_params = request_data.get('query_params', {})
        ip_address = request_data.get('ip_address', '')
        timestamp = request_data.get('timestamp', datetime.utcnow())
        
        # Extract URL features
        url_features = self._extract_url_features(url)
        
        # Extract parameter features
        param_features = self._extract_param_features(query_params, body)
        
        # Extract header features
        header_features = self._extract_header_features(headers)
        
        # Extract body features
        body_features = self._extract_body_features(body)
        
        # Extract request pattern features
        pattern_features = self._extract_pattern_features(method, headers)
        
        # Extract behavioral features
        behavioral_features = self._extract_behavioral_features(
            behavioral_data or {}
        )
        
        # Extract geographic features
        geo_features = self._extract_geographic_features(request_data)
        
        # Extract time-based features
        time_features = self._extract_time_features(timestamp)
        
        # Combine all features
        return RequestFeatures(
            **url_features,
            **param_features,
            **header_features,
            **body_features,
            **pattern_features,
            **behavioral_features,
            **geo_features,
            **time_features
        )
    
    def _extract_url_features(self, url: str) -> Dict[str, Any]:
        """Extract features from URL"""
        parsed = urlparse(url)
        path = parsed.path
        
        # URL length and depth
        url_length = len(url)
        url_depth = len([p for p in path.split('/') if p])
        
        # Count suspicious characters
        suspicious_chars = sum(1 for c in url if c in '&=?<>"\';(){}[]|\\^`~')
        
        # Calculate entropy
        url_entropy = self._calculate_entropy(url)
        
        # Check for suspicious extensions
        has_suspicious_ext = any(url.lower().endswith(ext) for ext in self.suspicious_extensions)
        
        # Count parameters
        params = parse_qs(parsed.query)
        param_count = len(params)
        
        return {
            'url_length': url_length,
            'url_depth': url_depth,
            'url_params_count': param_count,
            'url_suspicious_chars': suspicious_chars,
            'url_entropy': url_entropy,
            'has_suspicious_extensions': has_suspicious_ext
        }
    
    def _extract_param_features(self, query_params: Dict[str, Any], body: str) -> Dict[str, Any]:
        """Extract features from parameters"""
        # Combine query params and body params
        all_params = {}
        all_params.update(query_params)
        
        # Try to parse body as form data
        if body and 'application/x-www-form-urlencoded' in str(body):
            try:
                body_params = parse_qs(body)
                all_params.update(body_params)
            except:
                pass
        
        if not all_params:
            return {
                'param_count': 0,
                'param_total_length': 0,
                'param_max_length': 0,
                'param_avg_length': 0.0,
                'param_entropy': 0.0,
                'has_sql_keywords': False,
                'has_script_tags': False,
                'has_encoded_chars': False
            }
        
        # Calculate parameter statistics
        param_values = []
        for key, value in all_params.items():
            if isinstance(value, list):
                param_values.extend([str(v) for v in value])
            else:
                param_values.append(str(value))
            param_values.append(str(key))
        
        param_lengths = [len(p) for p in param_values]
        param_count = len(all_params)
        param_total_length = sum(param_lengths)
        param_max_length = max(param_lengths) if param_lengths else 0
        param_avg_length = param_total_length / len(param_values) if param_values else 0
        
        # Calculate entropy of all parameter data
        all_param_text = ' '.join(param_values)
        param_entropy = self._calculate_entropy(all_param_text)
        
        # Check for SQL keywords
        has_sql_keywords = any(
            keyword in all_param_text.lower() 
            for keyword in self.sql_keywords
        )
        
        # Check for script tags
        has_script_tags = any(
            re.search(pattern, all_param_text, re.IGNORECASE)
            for pattern in self.script_patterns
        )
        
        # Check for encoded characters
        has_encoded_chars = '%' in all_param_text or '&#' in all_param_text
        
        return {
            'param_count': param_count,
            'param_total_length': param_total_length,
            'param_max_length': param_max_length,
            'param_avg_length': param_avg_length,
            'param_entropy': param_entropy,
            'has_sql_keywords': has_sql_keywords,
            'has_script_tags': has_script_tags,
            'has_encoded_chars': has_encoded_chars
        }
    
    def _extract_header_features(self, headers: Dict[str, str]) -> Dict[str, Any]:
        """Extract features from HTTP headers"""
        headers_lower = {k.lower(): v for k, v in headers.items()}
        
        user_agent = headers_lower.get('user-agent', '')
        user_agent_length = len(user_agent)
        user_agent_entropy = self._calculate_entropy(user_agent)
        
        # Check if user agent looks like a bot
        is_bot_user_agent = any(
            re.search(pattern, user_agent, re.IGNORECASE)
            for pattern in self.bot_patterns
        )
        
        # Count missing common headers
        missing_common_headers = sum(
            1 for header in self.common_headers
            if header not in headers_lower
        )
        
        # Count suspicious headers
        suspicious_headers = 0
        suspicious_header_patterns = [
            r'x-forwarded', r'x-real-ip', r'x-originating-ip',
            r'client-ip', r'x-cluster-client-ip'
        ]
        
        for header_name in headers_lower.keys():
            if any(re.search(pattern, header_name, re.IGNORECASE) 
                   for pattern in suspicious_header_patterns):
                suspicious_headers += 1
        
        return {
            'user_agent_length': user_agent_length,
            'user_agent_entropy': user_agent_entropy,
            'is_bot_user_agent': is_bot_user_agent,
            'missing_common_headers': missing_common_headers,
            'suspicious_headers': suspicious_headers
        }
    
    def _extract_body_features(self, body: str) -> Dict[str, Any]:
        """Extract features from request body"""
        body_length = len(body)
        body_entropy = self._calculate_entropy(body) if body else 0.0
        
        # Check for binary content
        has_binary_content = False
        if body:
            try:
                body.encode('utf-8')
            except UnicodeDecodeError:
                has_binary_content = True
        
        return {
            'body_length': body_length,
            'body_entropy': body_entropy,
            'has_binary_content': has_binary_content
        }
    
    def _extract_pattern_features(self, method: str, headers: Dict[str, str]) -> Dict[str, Any]:
        """Extract request pattern features"""
        # Method risk scoring
        method_risk_scores = {
            'GET': 0.1,
            'POST': 0.3,
            'PUT': 0.5,
            'DELETE': 0.7,
            'PATCH': 0.4,
            'HEAD': 0.1,
            'OPTIONS': 0.2,
            'TRACE': 0.9,
            'CONNECT': 0.9
        }
        
        method_risk_score = method_risk_scores.get(method.upper(), 0.5)
        
        # Content type risk
        content_type = headers.get('content-type', '').lower()
        content_type_risk = 0.0
        
        if 'application/x-www-form-urlencoded' in content_type:
            content_type_risk = 0.3
        elif 'multipart/form-data' in content_type:
            content_type_risk = 0.4
        elif 'application/json' in content_type:
            content_type_risk = 0.2
        elif 'text/xml' in content_type or 'application/xml' in content_type:
            content_type_risk = 0.5
        
        return {
            'method_risk_score': method_risk_score,
            'content_type_risk': content_type_risk
        }
    
    def _extract_behavioral_features(self, behavioral_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract behavioral features"""
        return {
            'request_frequency': behavioral_data.get('request_frequency', 0.0),
            'unique_params_ratio': behavioral_data.get('unique_params_ratio', 0.0),
            'error_rate': behavioral_data.get('error_rate', 0.0)
        }
    
    def _extract_geographic_features(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract geographic features"""
        country = request_data.get('country', '')
        is_tor = request_data.get('is_tor', False)
        is_vpn = request_data.get('is_vpn', False)
        is_proxy = request_data.get('is_proxy', False)
        
        # Country risk scoring
        country_risk_score = 0.8 if country in self.high_risk_countries else 0.2
        
        return {
            'is_tor': is_tor,
            'is_vpn': is_vpn,
            'is_proxy': is_proxy,
            'country_risk_score': country_risk_score
        }
    
    def _extract_time_features(self, timestamp: datetime) -> Dict[str, Any]:
        """Extract time-based features"""
        hour_of_day = timestamp.hour
        day_of_week = timestamp.weekday()
        is_weekend = day_of_week >= 5
        is_night_time = hour_of_day < 6 or hour_of_day > 22
        
        return {
            'hour_of_day': hour_of_day,
            'day_of_week': day_of_week,
            'is_weekend': is_weekend,
            'is_night_time': is_night_time
        }
    
    def _calculate_entropy(self, text: str) -> float:
        """Calculate Shannon entropy of text"""
        if not text:
            return 0.0
        
        # Count character frequencies
        char_counts = {}
        for char in text:
            char_counts[char] = char_counts.get(char, 0) + 1
        
        # Calculate entropy
        text_length = len(text)
        entropy = 0.0
        
        for count in char_counts.values():
            probability = count / text_length
            if probability > 0:
                entropy -= probability * math.log2(probability)
        
        return entropy
    
    def features_to_vector(self, features: RequestFeatures) -> np.ndarray:
        """Convert features to numpy vector for ML models"""
        # Convert boolean features to integers
        vector = [
            features.url_length,
            features.url_depth,
            features.url_params_count,
            features.url_suspicious_chars,
            features.url_entropy,
            int(features.has_suspicious_extensions),
            features.param_count,
            features.param_total_length,
            features.param_max_length,
            features.param_avg_length,
            features.param_entropy,
            int(features.has_sql_keywords),
            int(features.has_script_tags),
            int(features.has_encoded_chars),
            features.user_agent_length,
            features.user_agent_entropy,
            int(features.is_bot_user_agent),
            features.missing_common_headers,
            features.suspicious_headers,
            features.body_length,
            features.body_entropy,
            int(features.has_binary_content),
            features.method_risk_score,
            features.content_type_risk,
            features.request_frequency,
            features.unique_params_ratio,
            features.error_rate,
            int(features.is_tor),
            int(features.is_vpn),
            int(features.is_proxy),
            features.country_risk_score,
            features.hour_of_day,
            features.day_of_week,
            int(features.is_weekend),
            int(features.is_night_time)
        ]
        
        return np.array(vector, dtype=np.float32)
