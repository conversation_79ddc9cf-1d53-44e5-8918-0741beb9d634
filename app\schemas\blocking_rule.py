"""
Blocking rule schemas for request/response models
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field, IPvAnyAddress, validator
from enum import Enum


class RuleType(str, Enum):
    """Blocking rule type enumeration"""
    IP = "ip"
    IP_RANGE = "ip_range"
    COUNTRY = "country"
    REGION = "region"
    ISP = "isp"
    USER_AGENT = "user_agent"
    DEVICE_TYPE = "device_type"
    THREAT_SCORE = "threat_score"
    CUSTOM = "custom"


class RuleAction(str, Enum):
    """Rule action enumeration"""
    BLOCK = "block"
    ALLOW = "allow"
    MONITOR = "monitor"
    RATE_LIMIT = "rate_limit"
    CAPTCHA = "captcha"
    REDIRECT = "redirect"


class RuleScope(str, Enum):
    """Rule scope enumeration"""
    GLOBAL = "global"
    WEBSITE = "website"
    PATH = "path"
    ENDPOINT = "endpoint"


class BlockingRuleCreate(BaseModel):
    """Schema for creating a new blocking rule"""
    website_id: Optional[UUID] = Field(None, description="Website ID (null for global rules)")
    rule_type: RuleType
    rule_value: str = Field(..., max_length=255, description="Value to match against")
    action: RuleAction = RuleAction.BLOCK
    scope: RuleScope = RuleScope.WEBSITE
    
    # Rule configuration
    is_active: bool = True
    priority: int = Field(100, ge=1, le=1000, description="Rule priority (1=highest, 1000=lowest)")
    expires_at: Optional[datetime] = Field(None, description="Rule expiration time")
    
    # Action parameters
    action_parameters: Optional[Dict[str, Any]] = Field(None, description="Action-specific parameters")
    
    # Metadata
    name: Optional[str] = Field(None, max_length=255, description="Human-readable rule name")
    description: Optional[str] = Field(None, max_length=1000, description="Rule description")
    tags: Optional[List[str]] = Field(None, description="Rule tags for organization")
    
    # Rate limiting specific
    rate_limit_requests: Optional[int] = Field(None, ge=1, description="Requests per time window")
    rate_limit_window: Optional[int] = Field(None, ge=1, description="Time window in seconds")
    
    @validator('rule_value')
    def validate_rule_value(cls, v, values):
        """Validate rule value based on rule type"""
        rule_type = values.get('rule_type')
        
        if rule_type == RuleType.IP:
            # Basic IP validation (IPv4/IPv6)
            try:
                import ipaddress
                ipaddress.ip_address(v)
            except ValueError:
                raise ValueError('Invalid IP address format')
        
        elif rule_type == RuleType.IP_RANGE:
            # CIDR notation validation
            try:
                import ipaddress
                ipaddress.ip_network(v, strict=False)
            except ValueError:
                raise ValueError('Invalid IP range format (use CIDR notation)')
        
        elif rule_type == RuleType.COUNTRY:
            # Country code validation (ISO 3166-1 alpha-2)
            if len(v) != 2 or not v.isalpha():
                raise ValueError('Country code must be 2-letter ISO code')
            v = v.upper()
        
        return v


class BlockingRuleUpdate(BaseModel):
    """Schema for updating a blocking rule"""
    rule_value: Optional[str] = Field(None, max_length=255)
    action: Optional[RuleAction] = None
    scope: Optional[RuleScope] = None
    is_active: Optional[bool] = None
    priority: Optional[int] = Field(None, ge=1, le=1000)
    expires_at: Optional[datetime] = None
    action_parameters: Optional[Dict[str, Any]] = None
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    tags: Optional[List[str]] = None
    rate_limit_requests: Optional[int] = Field(None, ge=1)
    rate_limit_window: Optional[int] = Field(None, ge=1)


class BlockingRuleResponse(BaseModel):
    """Schema for blocking rule response"""
    id: UUID
    website_id: Optional[UUID]
    rule_type: str
    rule_value: str
    action: str
    scope: str
    is_active: bool
    priority: int
    expires_at: Optional[datetime]
    action_parameters: Optional[Dict[str, Any]]
    name: Optional[str]
    description: Optional[str]
    tags: Optional[List[str]]
    rate_limit_requests: Optional[int]
    rate_limit_window: Optional[int]
    
    # Statistics
    matches_count: int = 0
    last_match: Optional[datetime] = None
    
    # Timestamps
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class BlockingRuleDetailResponse(BlockingRuleResponse):
    """Detailed blocking rule response with additional information"""
    website_info: Optional[Dict[str, str]] = None
    recent_matches: List[Dict[str, Any]] = []
    effectiveness_score: float = 0.0
    false_positive_rate: float = 0.0


class BlockingRuleListResponse(BaseModel):
    """Response for blocking rule listing with pagination"""
    rules: List[BlockingRuleResponse]
    total: int
    page: int
    per_page: int
    pages: int


class BlockingRuleMatch(BaseModel):
    """Blocking rule match event"""
    rule_id: UUID
    website_id: Optional[UUID]
    visitor_id: Optional[UUID]
    ip_address: str
    user_agent: Optional[str]
    matched_value: str
    action_taken: str
    blocked: bool
    matched_at: datetime
    additional_data: Optional[Dict[str, Any]] = None


class BlockingRuleStats(BaseModel):
    """Blocking rule statistics"""
    total_rules: int
    active_rules: int
    expired_rules: int
    rules_by_type: Dict[str, int]
    rules_by_action: Dict[str, int]
    total_matches: int
    blocked_requests: int
    allowed_requests: int
    top_matching_rules: List[Dict[str, Any]]


class BlockingRuleFilter(BaseModel):
    """Blocking rule filtering options"""
    website_id: Optional[UUID] = None
    rule_type: Optional[RuleType] = None
    action: Optional[RuleAction] = None
    scope: Optional[RuleScope] = None
    is_active: Optional[bool] = None
    is_expired: Optional[bool] = None
    tags: Optional[List[str]] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None


class BlockingRuleBulkAction(BaseModel):
    """Bulk action on blocking rules"""
    rule_ids: List[UUID]
    action: str = Field(..., description="Action: activate, deactivate, delete, update_priority")
    parameters: Optional[Dict[str, Any]] = None


class BlockingRuleTemplate(BaseModel):
    """Blocking rule template for common scenarios"""
    name: str = Field(..., max_length=255)
    description: str = Field(..., max_length=1000)
    rule_type: RuleType
    action: RuleAction
    scope: RuleScope
    default_parameters: Dict[str, Any]
    tags: List[str] = []
    category: str = Field(..., max_length=100)


class BlockingRuleImport(BaseModel):
    """Import blocking rules from external sources"""
    source: str = Field(..., description="Source type: csv, json, threat_feed")
    data: str = Field(..., description="Raw data to import")
    website_id: Optional[UUID] = None
    default_action: RuleAction = RuleAction.BLOCK
    default_priority: int = Field(500, ge=1, le=1000)
    overwrite_existing: bool = False


class BlockingRuleExport(BaseModel):
    """Export blocking rules configuration"""
    website_id: Optional[UUID] = None
    rule_types: Optional[List[RuleType]] = None
    format: str = Field("json", description="Export format: json, csv, yaml")
    include_stats: bool = False
    include_inactive: bool = False
