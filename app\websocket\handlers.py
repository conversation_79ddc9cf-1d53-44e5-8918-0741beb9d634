"""
WebSocket message handlers
"""

import json
import logging
from typing import Dict, Any, Optional
from uuid import UUI<PERSON>

from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from app.websocket.manager import websocket_manager
from app.websocket.events import (
    EventType, EventSubscription, EventFilter, WebSocketEvent
)
from app.core.security import decode_access_token
from app.core.database import get_db

logger = logging.getLogger(__name__)


class WebSocketHandler:
    """WebSocket message handler"""
    
    def __init__(self):
        self.message_handlers = {
            "subscribe": self._handle_subscribe,
            "unsubscribe": self._handle_unsubscribe,
            "subscribe_website": self._handle_subscribe_website,
            "unsubscribe_website": self._handle_unsubscribe_website,
            "get_stats": self._handle_get_stats,
            "get_recent_events": self._handle_get_recent_events,
            "ping": self._handle_ping
        }
    
    async def handle_websocket(self, websocket: WebSocket, token: str):
        """Handle WebSocket connection lifecycle"""
        connection_id = None
        
        try:
            # Authenticate user
            user_data = decode_access_token(token)
            if not user_data or not user_data.get("sub"):
                await websocket.close(code=4001, reason="Invalid token")
                return
            
            user_id = UUID(user_data["sub"])
            
            # Accept connection
            connection_id = await websocket_manager.connect(websocket, user_id)
            
            # Handle messages
            while True:
                try:
                    # Receive message
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    # Process message
                    await self._process_message(connection_id, message)
                    
                except WebSocketDisconnect:
                    break
                except json.JSONDecodeError:
                    await self._send_error(websocket, "Invalid JSON format")
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {e}")
                    await self._send_error(websocket, "Internal server error")
        
        except Exception as e:
            logger.error(f"WebSocket connection error: {e}")
        
        finally:
            if connection_id:
                await websocket_manager.disconnect(connection_id)
    
    async def _process_message(self, connection_id: str, message: Dict[str, Any]):
        """Process incoming WebSocket message"""
        message_type = message.get("type")
        
        if message_type not in self.message_handlers:
            await self._send_error_to_connection(connection_id, f"Unknown message type: {message_type}")
            return
        
        try:
            handler = self.message_handlers[message_type]
            await handler(connection_id, message.get("data", {}))
        except Exception as e:
            logger.error(f"Error handling message type {message_type}: {e}")
            await self._send_error_to_connection(connection_id, "Error processing message")
    
    async def _handle_subscribe(self, connection_id: str, data: Dict[str, Any]):
        """Handle event subscription"""
        try:
            # Parse subscription data
            subscription_id = data.get("subscription_id", f"sub_{connection_id}")
            event_types = data.get("event_types", [])
            website_ids = data.get("website_ids", [])
            priority_levels = data.get("priority_levels", [])
            
            # Convert string UUIDs to UUID objects
            website_uuids = []
            for website_id in website_ids:
                try:
                    website_uuids.append(UUID(website_id))
                except ValueError:
                    continue
            
            # Create event filter
            event_filter = EventFilter(
                event_types=[EventType(et) for et in event_types if et in EventType.__members__],
                website_ids=website_uuids if website_uuids else None,
                priority_levels=priority_levels if priority_levels else None,
                include_historical=data.get("include_historical", False),
                max_events=data.get("max_events", 100)
            )
            
            # Get user ID from connection
            connection = websocket_manager.connections.get(connection_id)
            if not connection:
                return
            
            # Create subscription
            subscription = EventSubscription(
                subscription_id=subscription_id,
                user_id=connection.user_id,
                filters=event_filter
            )
            
            # Add subscription
            await websocket_manager.add_subscription(connection_id, subscription)
            
            # Send confirmation
            await self._send_response(connection_id, {
                "type": "subscription_confirmed",
                "subscription_id": subscription_id,
                "filters": event_filter.dict()
            })
            
            # Send historical events if requested
            if event_filter.include_historical:
                recent_events = await websocket_manager.get_recent_events(event_filter.max_events)
                for event in recent_events:
                    if subscription.matches_event(event):
                        await connection.send_event(event)
        
        except Exception as e:
            logger.error(f"Error handling subscribe: {e}")
            await self._send_error_to_connection(connection_id, "Error creating subscription")
    
    async def _handle_unsubscribe(self, connection_id: str, data: Dict[str, Any]):
        """Handle event unsubscription"""
        subscription_id = data.get("subscription_id")
        if not subscription_id:
            await self._send_error_to_connection(connection_id, "Missing subscription_id")
            return
        
        await websocket_manager.remove_subscription(connection_id, subscription_id)
        
        await self._send_response(connection_id, {
            "type": "unsubscription_confirmed",
            "subscription_id": subscription_id
        })
    
    async def _handle_subscribe_website(self, connection_id: str, data: Dict[str, Any]):
        """Handle website-specific subscription"""
        website_id = data.get("website_id")
        if not website_id:
            await self._send_error_to_connection(connection_id, "Missing website_id")
            return
        
        try:
            website_uuid = UUID(website_id)
            await websocket_manager.subscribe_to_website(connection_id, website_uuid)
            
            await self._send_response(connection_id, {
                "type": "website_subscription_confirmed",
                "website_id": website_id
            })
        except ValueError:
            await self._send_error_to_connection(connection_id, "Invalid website_id format")
    
    async def _handle_unsubscribe_website(self, connection_id: str, data: Dict[str, Any]):
        """Handle website unsubscription"""
        website_id = data.get("website_id")
        if not website_id:
            await self._send_error_to_connection(connection_id, "Missing website_id")
            return
        
        try:
            website_uuid = UUID(website_id)
            await websocket_manager.unsubscribe_from_website(connection_id, website_uuid)
            
            await self._send_response(connection_id, {
                "type": "website_unsubscription_confirmed",
                "website_id": website_id
            })
        except ValueError:
            await self._send_error_to_connection(connection_id, "Invalid website_id format")
    
    async def _handle_get_stats(self, connection_id: str, data: Dict[str, Any]):
        """Handle stats request"""
        stats = await websocket_manager.get_connection_stats()
        
        await self._send_response(connection_id, {
            "type": "stats",
            "data": stats
        })
    
    async def _handle_get_recent_events(self, connection_id: str, data: Dict[str, Any]):
        """Handle recent events request"""
        limit = data.get("limit", 50)
        events = await websocket_manager.get_recent_events(limit)
        
        await self._send_response(connection_id, {
            "type": "recent_events",
            "data": [event.dict() for event in events]
        })
    
    async def _handle_ping(self, connection_id: str, data: Dict[str, Any]):
        """Handle ping message"""
        await self._send_response(connection_id, {
            "type": "pong",
            "timestamp": data.get("timestamp")
        })
    
    async def _send_response(self, connection_id: str, response: Dict[str, Any]):
        """Send response to specific connection"""
        connection = websocket_manager.connections.get(connection_id)
        if connection:
            await connection.send_json(response)
    
    async def _send_error_to_connection(self, connection_id: str, error_message: str):
        """Send error message to specific connection"""
        await self._send_response(connection_id, {
            "type": "error",
            "message": error_message
        })
    
    async def _send_error(self, websocket: WebSocket, error_message: str):
        """Send error message to WebSocket"""
        try:
            await websocket.send_json({
                "type": "error",
                "message": error_message
            })
        except Exception:
            pass  # Connection might be closed


# Global handler instance
websocket_handler = WebSocketHandler()
