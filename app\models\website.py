"""
Website model for monitoring configuration
"""

from sqlalchemy import Column, String, <PERSON><PERSON><PERSON>, DateTime, Integer, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from app.core.database import Base


class Website(Base):
    __tablename__ = "websites"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Website information
    domain = Column(String(255), nullable=False, index=True)
    url = Column(String(500), nullable=False)
    name = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    
    # Configuration
    is_active = Column(Boolean, default=True, nullable=False)
    scan_frequency = Column(Integer, default=3600, nullable=False)  # seconds
    monitoring_enabled = Column(<PERSON>ole<PERSON>, default=True, nullable=False)
    
    # SSL/Security settings
    ssl_monitoring = Column(Boolean, default=True, nullable=False)
    security_headers_check = Column(Boolean, default=True, nullable=False)
    
    # Scan configuration
    max_crawl_depth = Column(Integer, default=3, nullable=False)
    crawl_delay = Column(Integer, default=1, nullable=False)  # seconds
    respect_robots_txt = Column(Boolean, default=True, nullable=False)
    
    # Additional configuration as JSON
    config = Column(JSONB, nullable=True)
    
    # Status tracking
    last_scan_at = Column(DateTime(timezone=True), nullable=True)
    last_health_check = Column(DateTime(timezone=True), nullable=True)
    status = Column(String(50), default="active", nullable=False)  # active, inactive, error
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="websites")
    scans = relationship("Scan", back_populates="website", cascade="all, delete-orphan")
    visitors = relationship("Visitor", back_populates="website", cascade="all, delete-orphan")
    threats = relationship("Threat", back_populates="website", cascade="all, delete-orphan")
    blocking_rules = relationship("BlockingRule", back_populates="website", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Website(domain='{self.domain}', name='{self.name}')>"
