"""
Database configuration and session management
Optimized for Railway PostgreSQL deployment
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import asyncio
from typing import AsyncGenerator

from app.core.config import settings

# Database engine configuration
engine_kwargs = {
    "pool_pre_ping": True,
    "pool_recycle": 300,
    "pool_size": 10,
    "max_overflow": 20,
}

# For Railway deployment, use connection pooling
if settings.ENVIRONMENT == "production":
    engine_kwargs.update({
        "pool_size": 5,
        "max_overflow": 10,
        "pool_timeout": 30,
    })

# Create database engine
engine = create_engine(settings.DB_URL, **engine_kwargs)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# Metadata for migrations
metadata = MetaData()


def get_db():
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def create_tables():
    """Create database tables"""
    try:
        # Import all models to ensure they're registered
        from app.models import user, website, visitor, scan, threat, blocking_rule

        # Create tables
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created successfully")
    except Exception as e:
        print(f"⚠️  Database not available: {e}")
        print("🔄 Application will continue without database (API endpoints will be limited)")
        # Don't raise the exception - allow the app to start without database


async def check_db_connection():
    """Check database connection health"""
    try:
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False


class DatabaseManager:
    """Database management utilities"""
    
    @staticmethod
    def get_connection_info():
        """Get database connection information"""
        return {
            "url": settings.DB_URL.split("@")[-1] if "@" in settings.DB_URL else "localhost",
            "environment": settings.ENVIRONMENT,
            "pool_size": engine.pool.size(),
            "checked_out": engine.pool.checkedout(),
        }
    
    @staticmethod
    async def health_check():
        """Comprehensive database health check"""
        try:
            db = SessionLocal()
            
            # Test basic connection
            result = db.execute("SELECT version()").fetchone()
            
            # Test table existence
            tables_query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
            """
            tables = db.execute(tables_query).fetchall()
            
            db.close()
            
            return {
                "status": "healthy",
                "version": result[0] if result else "unknown",
                "tables_count": len(tables),
                "connection_pool": {
                    "size": engine.pool.size(),
                    "checked_out": engine.pool.checkedout(),
                    "overflow": engine.pool.overflow(),
                }
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }


# Global database manager instance
db_manager = DatabaseManager()
