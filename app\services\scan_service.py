"""
Scan service for business logic
"""

import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException, status
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

from app.models.scan import Scan
from app.models.website import Website
from app.schemas.scan import (
    ScanCreate, ScanUpdate, ScanResponse, ScanDetailResponse,
    ScanListResponse, URLDiscoveryResults, ScanProgress, ScanFilter
)
from app.services.base_service import BaseService


class ScanService(BaseService[Scan, ScanCreate, ScanUpdate]):
    """Scan service with business logic"""
    
    def __init__(self):
        super().__init__(Scan)
    
    async def create_scan(
        self,
        db: Session,
        *,
        scan_data: ScanCreate,
        user_id: UUID
    ) -> Scan:
        """Create a new scan"""
        # Verify website ownership
        website = db.query(Website).filter(
            and_(Website.id == scan_data.website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Website not found or access denied"
            )
        
        # Create scan
        scan = await self.create(db, obj_in=scan_data)
        
        # Start scan asynchronously
        asyncio.create_task(self._execute_scan(scan.id, website.url, scan_data.scan_type))
        
        return scan
    
    async def get_user_scans(
        self,
        db: Session,
        *,
        user_id: UUID,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[ScanFilter] = None
    ) -> ScanListResponse:
        """Get scans for a user"""
        # Build query
        query = db.query(Scan).join(Website).filter(Website.user_id == user_id)
        
        if filters:
            query = self._apply_scan_filters(query, filters)
        
        # Get total count
        total = query.count()
        
        # Get paginated results
        scans = query.order_by(desc(Scan.created_at)).offset(skip).limit(limit).all()
        
        pages = (total + limit - 1) // limit
        
        return ScanListResponse(
            scans=[ScanResponse.from_orm(s) for s in scans],
            total=total,
            page=(skip // limit) + 1,
            per_page=limit,
            pages=pages
        )
    
    async def get_scan_detail(
        self,
        db: Session,
        *,
        scan_id: UUID,
        user_id: UUID
    ) -> ScanDetailResponse:
        """Get detailed scan information"""
        scan = await self.get_or_404(db, scan_id)
        
        # Verify access through website ownership
        website = db.query(Website).filter(
            and_(Website.id == scan.website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Get additional scan details
        detail_data = ScanResponse.from_orm(scan).dict()
        
        # Add website info
        detail_data["website_info"] = {
            "domain": website.domain,
            "name": website.name
        }
        
        # Calculate duration
        if scan.started_at and scan.completed_at:
            duration = int((scan.completed_at - scan.started_at).total_seconds())
            detail_data["duration"] = duration
        
        # Extract metrics from results
        if scan.results:
            if scan.scan_type == "url_discovery":
                detail_data["pages_scanned"] = len(scan.results.get("urls", []))
                detail_data["urls_discovered"] = len(scan.results.get("urls", []))
            elif scan.scan_type == "security_scan":
                detail_data["vulnerabilities_found"] = len(scan.results.get("vulnerabilities", []))
        
        return ScanDetailResponse(**detail_data)
    
    async def get_scan_progress(
        self,
        db: Session,
        *,
        scan_id: UUID,
        user_id: UUID
    ) -> ScanProgress:
        """Get real-time scan progress"""
        scan = await self.get_or_404(db, scan_id)
        
        # Verify access through website ownership
        website = db.query(Website).filter(
            and_(Website.id == scan.website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Calculate estimated completion
        estimated_completion = None
        if scan.started_at and scan.progress > 0:
            elapsed = datetime.utcnow() - scan.started_at
            total_estimated = elapsed / (scan.progress / 100)
            estimated_completion = scan.started_at + total_estimated
        
        return ScanProgress(
            scan_id=scan.id,
            status=scan.status,
            progress=scan.progress,
            current_task=scan.results.get("current_task") if scan.results else None,
            pages_scanned=scan.results.get("pages_scanned", 0) if scan.results else 0,
            total_pages=scan.results.get("total_pages") if scan.results else None,
            errors_count=scan.results.get("errors_count", 0) if scan.results else 0,
            estimated_completion=estimated_completion,
            last_updated=scan.updated_at or scan.created_at
        )
    
    async def cancel_scan(
        self,
        db: Session,
        *,
        scan_id: UUID,
        user_id: UUID
    ) -> Scan:
        """Cancel a running scan"""
        scan = await self.get_or_404(db, scan_id)
        
        # Verify access through website ownership
        website = db.query(Website).filter(
            and_(Website.id == scan.website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Only cancel if scan is running or pending
        if scan.status not in ["pending", "running"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Scan cannot be cancelled in current status"
            )
        
        # Update scan status
        update_data = ScanUpdate(status="cancelled", completed_at=datetime.utcnow())
        return await self.update(db, db_obj=scan, obj_in=update_data)
    
    async def _execute_scan(self, scan_id: UUID, website_url: str, scan_type: str):
        """Execute scan asynchronously"""
        # This would be implemented with proper async task handling
        # For now, simulate scan execution
        try:
            if scan_type == "url_discovery":
                await self._execute_url_discovery_scan(scan_id, website_url)
            elif scan_type == "security_scan":
                await self._execute_security_scan(scan_id, website_url)
            elif scan_type == "performance_scan":
                await self._execute_performance_scan(scan_id, website_url)
            elif scan_type == "full_scan":
                await self._execute_full_scan(scan_id, website_url)
        except Exception as e:
            # Update scan with error
            # This would need proper database session handling
            pass
    
    async def _execute_url_discovery_scan(self, scan_id: UUID, website_url: str):
        """Execute URL discovery scan"""
        discovered_urls = []
        visited_urls = set()
        urls_to_visit = [website_url]
        max_depth = 3
        current_depth = 0
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                while urls_to_visit and current_depth < max_depth:
                    current_url = urls_to_visit.pop(0)
                    
                    if current_url in visited_urls:
                        continue
                    
                    visited_urls.add(current_url)
                    
                    try:
                        async with session.get(current_url) as response:
                            if response.status == 200:
                                content = await response.text()
                                soup = BeautifulSoup(content, 'html.parser')
                                
                                # Extract page info
                                title = soup.find('title')
                                title_text = title.get_text().strip() if title else ""
                                
                                meta_desc = soup.find('meta', attrs={'name': 'description'})
                                meta_description = meta_desc.get('content', '') if meta_desc else ""
                                
                                # Count elements
                                forms_count = len(soup.find_all('form'))
                                links_count = len(soup.find_all('a'))
                                images_count = len(soup.find_all('img'))
                                scripts_count = len(soup.find_all('script'))
                                
                                # Record URL info
                                url_info = {
                                    "url": current_url,
                                    "status_code": response.status,
                                    "response_time": 0.5,  # Simplified
                                    "content_type": response.headers.get('content-type', ''),
                                    "title": title_text,
                                    "meta_description": meta_description,
                                    "forms_count": forms_count,
                                    "links_count": links_count,
                                    "images_count": images_count,
                                    "scripts_count": scripts_count,
                                    "depth": current_depth,
                                    "discovered_at": datetime.utcnow().isoformat()
                                }
                                
                                discovered_urls.append(url_info)
                                
                                # Find new URLs to visit
                                for link in soup.find_all('a', href=True):
                                    href = link['href']
                                    full_url = urljoin(current_url, href)
                                    
                                    # Only follow internal links
                                    if urlparse(full_url).netloc == urlparse(website_url).netloc:
                                        if full_url not in visited_urls and full_url not in urls_to_visit:
                                            urls_to_visit.append(full_url)
                    
                    except Exception as e:
                        # Record error URL
                        error_info = {
                            "url": current_url,
                            "error": str(e),
                            "depth": current_depth
                        }
                        # Add to error list
                    
                    # Simulate progress update
                    await asyncio.sleep(0.1)
                
                current_depth += 1
            
            # Prepare results
            results = {
                "total_urls": len(discovered_urls),
                "unique_domains": 1,  # Simplified
                "max_depth_reached": current_depth,
                "scan_duration": 60,  # Simplified
                "urls": discovered_urls,
                "sitemap_urls": [],
                "robots_txt_content": None,
                "forms_discovered": [],
                "api_endpoints": [],
                "error_urls": []
            }
            
            # Update scan with results (would need proper DB session)
            # This is simplified - in real implementation, would update via proper service
            
        except Exception as e:
            # Handle scan error
            pass
    
    async def _execute_security_scan(self, scan_id: UUID, website_url: str):
        """Execute security scan"""
        # Simplified security scan implementation
        vulnerabilities = []
        
        try:
            async with aiohttp.ClientSession() as session:
                # Check for common security headers
                async with session.get(website_url) as response:
                    headers = response.headers
                    
                    # Check for missing security headers
                    security_headers = [
                        'X-Content-Type-Options',
                        'X-Frame-Options',
                        'X-XSS-Protection',
                        'Strict-Transport-Security',
                        'Content-Security-Policy'
                    ]
                    
                    for header in security_headers:
                        if header not in headers:
                            vulnerabilities.append({
                                "vulnerability_type": "missing_security_header",
                                "severity": "medium",
                                "confidence": 90,
                                "url": website_url,
                                "parameter": header,
                                "description": f"Missing {header} security header",
                                "recommendation": f"Add {header} header to improve security",
                                "cwe_id": "CWE-16"
                            })
            
            # Prepare results
            results = {
                "total_vulnerabilities": len(vulnerabilities),
                "critical_count": 0,
                "high_count": 0,
                "medium_count": len(vulnerabilities),
                "low_count": 0,
                "scan_duration": 30,
                "vulnerabilities": vulnerabilities,
                "security_score": max(0, 100 - len(vulnerabilities) * 10),
                "compliance_status": {},
                "recommendations": [
                    "Implement missing security headers",
                    "Regular security assessments",
                    "Keep software updated"
                ]
            }
            
            # Update scan with results (simplified)
            
        except Exception as e:
            # Handle scan error
            pass
    
    async def _execute_performance_scan(self, scan_id: UUID, website_url: str):
        """Execute performance scan"""
        # Simplified performance scan
        pass
    
    async def _execute_full_scan(self, scan_id: UUID, website_url: str):
        """Execute comprehensive scan"""
        # Run all scan types
        await self._execute_url_discovery_scan(scan_id, website_url)
        await self._execute_security_scan(scan_id, website_url)
        await self._execute_performance_scan(scan_id, website_url)
    
    def _apply_scan_filters(self, query, filters: ScanFilter):
        """Apply filters to scan query"""
        if filters.website_id:
            query = query.filter(Scan.website_id == filters.website_id)
        if filters.scan_type:
            query = query.filter(Scan.scan_type == filters.scan_type)
        if filters.status:
            query = query.filter(Scan.status == filters.status)
        if filters.priority:
            query = query.filter(Scan.priority == filters.priority)
        if filters.date_from:
            query = query.filter(Scan.created_at >= filters.date_from)
        if filters.date_to:
            query = query.filter(Scan.created_at <= filters.date_to)
        
        return query
