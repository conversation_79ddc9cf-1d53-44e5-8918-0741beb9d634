"""
Scan schemas for request/response models
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field, HttpUrl
from enum import Enum


class ScanType(str, Enum):
    """Scan type enumeration"""
    URL_DISCOVERY = "url_discovery"
    DATABASE_ANALYSIS = "database_analysis"
    SECURITY_SCAN = "security_scan"
    PERFORMANCE_SCAN = "performance_scan"
    FULL_SCAN = "full_scan"


class ScanStatus(str, Enum):
    """Scan status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class ScanPriority(str, Enum):
    """Scan priority enumeration"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class ScanCreate(BaseModel):
    """Schema for creating a new scan"""
    website_id: UUID
    scan_type: ScanType
    priority: ScanPriority = ScanPriority.NORMAL
    config: Optional[Dict[str, Any]] = Field(None, description="Scan-specific configuration")
    scheduled_at: Optional[datetime] = Field(None, description="Schedule scan for later")


class ScanUpdate(BaseModel):
    """Schema for updating scan information"""
    status: Optional[ScanStatus] = None
    progress: Optional[int] = Field(None, ge=0, le=100)
    results: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = Field(None, max_length=2000)
    completed_at: Optional[datetime] = None


class ScanResponse(BaseModel):
    """Schema for scan response"""
    id: UUID
    website_id: UUID
    scan_type: str
    status: str
    priority: str
    progress: int
    results: Optional[Dict[str, Any]]
    error_message: Optional[str]
    config: Optional[Dict[str, Any]]
    scheduled_at: Optional[datetime]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class ScanDetailResponse(ScanResponse):
    """Detailed scan response with additional information"""
    website_info: Optional[Dict[str, str]] = None
    duration: Optional[int] = None  # Duration in seconds
    pages_scanned: int = 0
    urls_discovered: int = 0
    vulnerabilities_found: int = 0
    performance_metrics: Optional[Dict[str, Any]] = None


class ScanListResponse(BaseModel):
    """Response for scan listing with pagination"""
    scans: List[ScanResponse]
    total: int
    page: int
    per_page: int
    pages: int


class URLDiscoveryResult(BaseModel):
    """URL discovery scan result"""
    url: HttpUrl
    status_code: int
    response_time: float
    content_type: Optional[str]
    title: Optional[str]
    meta_description: Optional[str]
    forms_count: int = 0
    links_count: int = 0
    images_count: int = 0
    scripts_count: int = 0
    depth: int = 0
    parent_url: Optional[str] = None
    discovered_at: datetime


class URLDiscoveryResults(BaseModel):
    """Complete URL discovery results"""
    scan_id: UUID
    website_id: UUID
    total_urls: int
    unique_domains: int
    max_depth_reached: int
    scan_duration: int
    urls: List[URLDiscoveryResult]
    sitemap_urls: List[str] = []
    robots_txt_content: Optional[str] = None
    forms_discovered: List[Dict[str, Any]] = []
    api_endpoints: List[Dict[str, Any]] = []
    error_urls: List[Dict[str, Any]] = []


class DatabaseAnalysisResult(BaseModel):
    """Database analysis scan result"""
    table_name: str
    column_count: int
    row_count: Optional[int]
    columns: List[Dict[str, Any]]
    indexes: List[Dict[str, Any]]
    foreign_keys: List[Dict[str, Any]]
    constraints: List[Dict[str, Any]]
    vulnerabilities: List[Dict[str, Any]] = []
    security_score: float = 0.0


class DatabaseAnalysisResults(BaseModel):
    """Complete database analysis results"""
    scan_id: UUID
    website_id: UUID
    database_type: str
    database_version: Optional[str]
    total_tables: int
    total_columns: int
    total_rows: Optional[int]
    scan_duration: int
    tables: List[DatabaseAnalysisResult]
    schema_diagram: Optional[str] = None
    security_summary: Dict[str, Any] = {}
    recommendations: List[str] = []


class SecurityScanResult(BaseModel):
    """Security scan result"""
    vulnerability_type: str
    severity: str
    confidence: int
    url: Optional[str]
    parameter: Optional[str]
    payload: Optional[str]
    description: str
    recommendation: str
    cwe_id: Optional[str]
    cvss_score: Optional[float]


class SecurityScanResults(BaseModel):
    """Complete security scan results"""
    scan_id: UUID
    website_id: UUID
    total_vulnerabilities: int
    critical_count: int
    high_count: int
    medium_count: int
    low_count: int
    scan_duration: int
    vulnerabilities: List[SecurityScanResult]
    security_score: float
    compliance_status: Dict[str, bool] = {}
    recommendations: List[str] = []


class ScanProgress(BaseModel):
    """Real-time scan progress"""
    scan_id: UUID
    status: ScanStatus
    progress: int
    current_task: Optional[str] = None
    pages_scanned: int = 0
    total_pages: Optional[int] = None
    errors_count: int = 0
    estimated_completion: Optional[datetime] = None
    last_updated: datetime


class ScanFilter(BaseModel):
    """Scan filtering options"""
    website_id: Optional[UUID] = None
    scan_type: Optional[ScanType] = None
    status: Optional[ScanStatus] = None
    priority: Optional[ScanPriority] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None


class ScanBulkAction(BaseModel):
    """Bulk action on scans"""
    scan_ids: List[UUID]
    action: str = Field(..., description="Action: cancel, delete, retry, pause, resume")
    parameters: Optional[Dict[str, Any]] = None


class ScanSchedule(BaseModel):
    """Scan scheduling configuration"""
    website_id: UUID
    scan_type: ScanType
    frequency: str = Field(..., description="Cron expression or predefined frequency")
    priority: ScanPriority = ScanPriority.NORMAL
    config: Optional[Dict[str, Any]] = None
    is_active: bool = True
    next_run: Optional[datetime] = None


class ScanScheduleResponse(ScanSchedule):
    """Scan schedule response"""
    id: UUID
    last_run: Optional[datetime] = None
    total_runs: int = 0
    success_rate: float = 0.0
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
