"""
Background tasks for website scanning
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List
from uuid import UUID

from celery import current_task
from sqlalchemy.orm import Session

from app.tasks.celery_app import celery_app
from app.core.database import SessionLocal
from app.models.scan import Scan
from app.models.website import Website
from app.services.scan_service import ScanService
from app.services.event_service import event_service
from app.websocket.events import EventType

logger = logging.getLogger(__name__)


def get_db_session():
    """Get database session for tasks"""
    return SessionLocal()


@celery_app.task(bind=True, name='app.tasks.scan_tasks.perform_url_discovery_task')
def perform_url_discovery_task(self, scan_id: str, website_url: str):
    """Perform URL discovery scan in background"""
    scan_uuid = UUID(scan_id)
    
    try:
        # Update task status
        self.update_state(state='PROGRESS', meta={'progress': 0, 'status': 'Starting URL discovery'})
        
        # Get database session
        db = get_db_session()
        
        try:
            # Get scan record
            scan = db.query(Scan).filter(Scan.id == scan_uuid).first()
            if not scan:
                raise ValueError(f"Scan {scan_id} not found")
            
            # Update scan status
            scan.status = "running"
            scan.started_at = datetime.utcnow()
            scan.progress = 0
            db.commit()
            
            # Emit scan started event
            asyncio.run(event_service.scan_started(
                scan_id=scan.id,
                website_id=scan.website_id,
                scan_type=scan.scan_type
            ))
            
            # Perform URL discovery
            discovered_urls = []
            visited_urls = set()
            urls_to_visit = [website_url]
            max_depth = 3
            current_depth = 0
            total_estimated = 100  # Estimate for progress calculation
            
            while urls_to_visit and current_depth < max_depth:
                current_url = urls_to_visit.pop(0)
                
                if current_url in visited_urls:
                    continue
                
                visited_urls.add(current_url)
                
                # Update progress
                progress = min(95, (len(visited_urls) / total_estimated) * 100)
                scan.progress = int(progress)
                db.commit()
                
                # Update task state
                self.update_state(
                    state='PROGRESS',
                    meta={
                        'progress': progress,
                        'status': f'Scanning {current_url}',
                        'urls_found': len(discovered_urls),
                        'current_depth': current_depth
                    }
                )
                
                # Emit progress event
                asyncio.run(event_service.scan_progress(
                    scan_id=scan.id,
                    website_id=scan.website_id,
                    scan_type=scan.scan_type,
                    progress=int(progress),
                    current_task=f'Scanning {current_url}',
                    pages_scanned=len(visited_urls)
                ))
                
                try:
                    # Simulate URL analysis (in real implementation, would use aiohttp)
                    import time
                    time.sleep(0.1)  # Simulate processing time
                    
                    # Mock discovered URL data
                    url_info = {
                        "url": current_url,
                        "status_code": 200,
                        "response_time": 0.5,
                        "content_type": "text/html",
                        "title": f"Page {len(discovered_urls) + 1}",
                        "meta_description": "Sample page description",
                        "forms_count": 1,
                        "links_count": 5,
                        "images_count": 3,
                        "scripts_count": 2,
                        "depth": current_depth,
                        "discovered_at": datetime.utcnow().isoformat()
                    }
                    
                    discovered_urls.append(url_info)
                    
                    # Add mock child URLs for first few pages
                    if len(discovered_urls) < 20:
                        for i in range(2):
                            child_url = f"{website_url}/page-{len(discovered_urls)}-{i}"
                            if child_url not in visited_urls and child_url not in urls_to_visit:
                                urls_to_visit.append(child_url)
                
                except Exception as e:
                    logger.error(f"Error scanning URL {current_url}: {e}")
                    continue
                
                # Check if task was revoked
                if self.is_revoked():
                    scan.status = "cancelled"
                    db.commit()
                    return {"status": "cancelled", "message": "Task was cancelled"}
            
            # Prepare final results
            results = {
                "total_urls": len(discovered_urls),
                "unique_domains": 1,
                "max_depth_reached": current_depth,
                "scan_duration": int((datetime.utcnow() - scan.started_at).total_seconds()),
                "urls": discovered_urls,
                "sitemap_urls": [],
                "robots_txt_content": None,
                "forms_discovered": [],
                "api_endpoints": [],
                "error_urls": []
            }
            
            # Update scan with results
            scan.status = "completed"
            scan.progress = 100
            scan.completed_at = datetime.utcnow()
            scan.results = results
            db.commit()
            
            # Emit completion event
            asyncio.run(event_service.scan_completed(
                scan_id=scan.id,
                website_id=scan.website_id,
                scan_type=scan.scan_type,
                results=results
            ))
            
            return {
                "status": "completed",
                "scan_id": scan_id,
                "results": results
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error in URL discovery task: {e}")
        
        # Update scan status to failed
        db = get_db_session()
        try:
            scan = db.query(Scan).filter(Scan.id == scan_uuid).first()
            if scan:
                scan.status = "failed"
                scan.error_message = str(e)
                scan.completed_at = datetime.utcnow()
                db.commit()
                
                # Emit failure event
                asyncio.run(event_service.scan_failed(
                    scan_id=scan.id,
                    website_id=scan.website_id,
                    scan_type=scan.scan_type,
                    error=str(e)
                ))
        finally:
            db.close()
        
        # Update task state
        self.update_state(
            state='FAILURE',
            meta={'error': str(e), 'scan_id': scan_id}
        )
        
        raise


@celery_app.task(bind=True, name='app.tasks.scan_tasks.perform_security_scan_task')
def perform_security_scan_task(self, scan_id: str, website_url: str):
    """Perform security scan in background"""
    scan_uuid = UUID(scan_id)
    
    try:
        self.update_state(state='PROGRESS', meta={'progress': 0, 'status': 'Starting security scan'})
        
        db = get_db_session()
        
        try:
            scan = db.query(Scan).filter(Scan.id == scan_uuid).first()
            if not scan:
                raise ValueError(f"Scan {scan_id} not found")
            
            scan.status = "running"
            scan.started_at = datetime.utcnow()
            scan.progress = 0
            db.commit()
            
            vulnerabilities = []
            security_checks = [
                "SSL/TLS Configuration",
                "Security Headers",
                "Input Validation",
                "Authentication",
                "Session Management",
                "Error Handling",
                "File Upload Security",
                "SQL Injection",
                "XSS Protection",
                "CSRF Protection"
            ]
            
            for i, check in enumerate(security_checks):
                progress = int((i + 1) / len(security_checks) * 100)
                scan.progress = progress
                db.commit()
                
                self.update_state(
                    state='PROGRESS',
                    meta={
                        'progress': progress,
                        'status': f'Checking {check}',
                        'vulnerabilities_found': len(vulnerabilities)
                    }
                )
                
                # Emit progress event
                asyncio.run(event_service.scan_progress(
                    scan_id=scan.id,
                    website_id=scan.website_id,
                    scan_type=scan.scan_type,
                    progress=progress,
                    current_task=f'Checking {check}'
                ))
                
                # Simulate security check
                import time
                time.sleep(0.5)
                
                # Mock vulnerability detection
                if i % 3 == 0:  # Find vulnerability every 3rd check
                    severity = ["low", "medium", "high"][i % 3]
                    vulnerabilities.append({
                        "vulnerability_type": check.lower().replace(" ", "_"),
                        "severity": severity,
                        "confidence": 85,
                        "url": website_url,
                        "description": f"Issue found in {check}",
                        "recommendation": f"Fix {check} configuration",
                        "cwe_id": f"CWE-{100 + i}"
                    })
            
            # Calculate security score
            total_checks = len(security_checks)
            vulnerabilities_found = len(vulnerabilities)
            security_score = max(0, 100 - (vulnerabilities_found / total_checks * 100))
            
            results = {
                "total_vulnerabilities": vulnerabilities_found,
                "critical_count": len([v for v in vulnerabilities if v["severity"] == "critical"]),
                "high_count": len([v for v in vulnerabilities if v["severity"] == "high"]),
                "medium_count": len([v for v in vulnerabilities if v["severity"] == "medium"]),
                "low_count": len([v for v in vulnerabilities if v["severity"] == "low"]),
                "scan_duration": int((datetime.utcnow() - scan.started_at).total_seconds()),
                "vulnerabilities": vulnerabilities,
                "security_score": security_score,
                "compliance_status": {
                    "OWASP_Top_10": security_score > 80,
                    "PCI_DSS": security_score > 90,
                    "GDPR": security_score > 85
                },
                "recommendations": [
                    "Implement proper input validation",
                    "Add security headers",
                    "Regular security assessments",
                    "Keep software updated"
                ]
            }
            
            scan.status = "completed"
            scan.progress = 100
            scan.completed_at = datetime.utcnow()
            scan.results = results
            db.commit()
            
            # Emit completion event
            asyncio.run(event_service.scan_completed(
                scan_id=scan.id,
                website_id=scan.website_id,
                scan_type=scan.scan_type,
                results=results
            ))
            
            return {
                "status": "completed",
                "scan_id": scan_id,
                "results": results
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error in security scan task: {e}")
        
        db = get_db_session()
        try:
            scan = db.query(Scan).filter(Scan.id == scan_uuid).first()
            if scan:
                scan.status = "failed"
                scan.error_message = str(e)
                scan.completed_at = datetime.utcnow()
                db.commit()
                
                asyncio.run(event_service.scan_failed(
                    scan_id=scan.id,
                    website_id=scan.website_id,
                    scan_type=scan.scan_type,
                    error=str(e)
                ))
        finally:
            db.close()
        
        self.update_state(state='FAILURE', meta={'error': str(e), 'scan_id': scan_id})
        raise


@celery_app.task(bind=True, name='app.tasks.scan_tasks.perform_performance_scan_task')
def perform_performance_scan_task(self, scan_id: str, website_url: str):
    """Perform performance scan in background"""
    # Similar implementation to security scan but focused on performance metrics
    return {"status": "completed", "message": "Performance scan completed"}


@celery_app.task(bind=True, name='app.tasks.scan_tasks.perform_full_scan_task')
def perform_full_scan_task(self, scan_id: str, website_url: str):
    """Perform comprehensive scan combining all scan types"""
    # This would orchestrate multiple scan types
    return {"status": "completed", "message": "Full scan completed"}
