"""
Blocking system models
"""

from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Integer, Text, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
from enum import Enum

from app.core.database import Base


class BlockingRuleType(str, Enum):
    """Types of blocking rules"""
    IP_ADDRESS = "ip_address"
    IP_RANGE = "ip_range"
    COUNTRY = "country"
    REGION = "region"
    ISP = "isp"
    USER_AGENT = "user_agent"
    DEVICE_FINGERPRINT = "device_fingerprint"
    RATE_LIMIT = "rate_limit"
    CUSTOM = "custom"


class BlockingAction(str, Enum):
    """Actions to take when rule is triggered"""
    BLOCK = "block"
    THROTTLE = "throttle"
    CAPTCHA = "captcha"
    LOG_ONLY = "log_only"
    REDIRECT = "redirect"


class BlockingRule(Base):
    """Blocking rules for security filtering"""
    __tablename__ = "blocking_rules"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    website_id = Column(UUID(as_uuid=True), ForeignKey("websites.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Rule identification
    name = Column(String(255), nullable=False)
    description = Column(Text)
    rule_type = Column(String(50), nullable=False)  # BlockingRuleType
    action = Column(String(50), nullable=False)  # BlockingAction
    
    # Rule configuration
    rule_value = Column(String(500), nullable=False)  # IP, country code, pattern, etc.
    rule_pattern = Column(String(1000))  # Regex pattern for custom rules
    rule_metadata = Column(JSON)  # Additional rule configuration
    
    # Timing and limits
    is_temporary = Column(Boolean, default=False)
    expires_at = Column(DateTime)
    rate_limit_requests = Column(Integer)  # Requests per time window
    rate_limit_window = Column(Integer)  # Time window in seconds
    
    # Status and priority
    is_active = Column(Boolean, default=True)
    priority = Column(Integer, default=100)  # Lower number = higher priority
    
    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(String(255))
    
    # Statistics
    trigger_count = Column(Integer, default=0)
    last_triggered = Column(DateTime)
    
    # Relationships
    website = relationship("Website", back_populates="blocking_rules")
    user = relationship("User")
    blocking_events = relationship("BlockingEvent", back_populates="blocking_rule")


class WhitelistEntry(Base):
    """Whitelist entries to bypass blocking rules"""
    __tablename__ = "whitelist_entries"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    website_id = Column(UUID(as_uuid=True), ForeignKey("websites.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Whitelist configuration
    entry_type = Column(String(50), nullable=False)  # ip_address, country, user_agent, etc.
    entry_value = Column(String(500), nullable=False)
    description = Column(Text)
    
    # Status and timing
    is_active = Column(Boolean, default=True)
    expires_at = Column(DateTime)
    
    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(String(255))
    
    # Statistics
    usage_count = Column(Integer, default=0)
    last_used = Column(DateTime)
    
    # Relationships
    website = relationship("Website")
    user = relationship("User")


class BlockingEvent(Base):
    """Log of blocking events and actions taken"""
    __tablename__ = "blocking_events"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    website_id = Column(UUID(as_uuid=True), ForeignKey("websites.id"), nullable=False)
    blocking_rule_id = Column(UUID(as_uuid=True), ForeignKey("blocking_rules.id"))
    
    # Event details
    event_type = Column(String(50), nullable=False)  # blocked, throttled, captcha, etc.
    source_ip = Column(String(45), nullable=False)
    user_agent = Column(Text)
    request_url = Column(Text)
    request_method = Column(String(10))
    
    # Geographic and intelligence data
    country = Column(String(2))
    region = Column(String(100))
    city = Column(String(100))
    isp = Column(String(255))
    is_vpn = Column(Boolean, default=False)
    is_proxy = Column(Boolean, default=False)
    is_tor = Column(Boolean, default=False)
    
    # Action taken
    action_taken = Column(String(50), nullable=False)
    action_details = Column(JSON)
    
    # Timing
    blocked_at = Column(DateTime, default=datetime.utcnow)
    duration_seconds = Column(Integer)  # How long the block lasted
    
    # Additional metadata
    event_metadata = Column(JSON)
    
    # Relationships
    website = relationship("Website")
    blocking_rule = relationship("BlockingRule", back_populates="blocking_events")


class RateLimitTracker(Base):
    """Track rate limiting for IPs and users"""
    __tablename__ = "rate_limit_trackers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    website_id = Column(UUID(as_uuid=True), ForeignKey("websites.id"), nullable=False)
    
    # Tracking key (IP, user ID, etc.)
    tracking_key = Column(String(255), nullable=False)
    tracking_type = Column(String(50), nullable=False)  # ip, user, session
    
    # Rate limiting data
    request_count = Column(Integer, default=0)
    window_start = Column(DateTime, default=datetime.utcnow)
    window_duration = Column(Integer, default=3600)  # Window duration in seconds
    
    # Status
    is_blocked = Column(Boolean, default=False)
    blocked_until = Column(DateTime)
    
    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    website = relationship("Website")
