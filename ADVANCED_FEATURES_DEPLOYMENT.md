# 🚀 ProSecurity Monitor - Advanced Features Deployment Guide

## 📋 Overview

This guide covers the deployment and configuration of the newly implemented advanced features:

1. **Real-time WebSocket System**
2. **AI-Powered Threat Detection**
3. **Advanced Geolocation & IP Intelligence**
4. **Background Task Processing with Celery**

## 🔧 Prerequisites

### System Requirements
- **Python**: 3.9+
- **Redis**: 6.0+ (for Celery and WebSocket scaling)
- **PostgreSQL**: 13+ (existing database)
- **Memory**: 4GB+ RAM (for ML models)
- **Storage**: 2GB+ additional space (for ML models and cache)

### New Dependencies
```bash
# Install new dependencies
pip install -r requirements.txt

# Key new packages:
# - scikit-learn>=1.3.0 (Machine Learning)
# - celery>=5.3.0 (Background Tasks)
# - redis>=4.6.0 (Message Broker)
# - websockets>=12.0 (WebSocket Support)
# - numpy>=1.24.0 (ML Support)
```

## 🐳 Docker Configuration Updates

### Updated docker-compose.yml
```yaml
version: '3.8'

services:
  # Existing services...
  
  # Redis for Celery and WebSocket scaling
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    
  # Celery Worker
  celery-worker:
    build: .
    command: celery -A app.tasks.celery_app worker --loglevel=info --concurrency=4
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      
  # Celery Beat (Scheduler)
  celery-beat:
    build: .
    command: celery -A app.tasks.celery_app beat --loglevel=info
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - CELERY_BROKER_URL=redis://redis:6379/0
      
  # Celery Flower (Monitoring)
  celery-flower:
    build: .
    command: celery -A app.tasks.celery_app flower --port=5555
    ports:
      - "5555:5555"
    depends_on:
      - redis
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0

volumes:
  redis_data:
```

## ⚙️ Environment Configuration

### Updated .env file
```bash
# Existing environment variables...

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# IP Intelligence APIs (Optional)
IPINFO_TOKEN=your_ipinfo_token_here
MAXMIND_LICENSE_KEY=your_maxmind_license_key

# WebSocket Configuration
WEBSOCKET_MAX_CONNECTIONS=1000
WEBSOCKET_HEARTBEAT_INTERVAL=30

# ML Model Configuration
ML_MODEL_PATH=models/
ML_TRAINING_ENABLED=true
ML_AUTO_RETRAIN=true

# Background Task Configuration
CELERY_TASK_SOFT_TIME_LIMIT=300
CELERY_TASK_TIME_LIMIT=600
CELERY_WORKER_CONCURRENCY=4
```

## 🚀 Deployment Steps

### 1. Install Dependencies
```bash
# Update requirements
pip install -r requirements.txt

# Install Redis (if not using Docker)
# Ubuntu/Debian:
sudo apt-get install redis-server

# macOS:
brew install redis

# Windows:
# Download from https://redis.io/download
```

### 2. Database Migration
```bash
# No new migrations needed - all features use existing models
# But run this to ensure everything is up to date:
alembic upgrade head
```

### 3. Start Redis
```bash
# If not using Docker:
redis-server

# Verify Redis is running:
redis-cli ping
# Should return: PONG
```

### 4. Start Celery Workers
```bash
# Start Celery worker (in separate terminal)
celery -A app.tasks.celery_app worker --loglevel=info --concurrency=4

# Start Celery Beat scheduler (in separate terminal)
celery -A app.tasks.celery_app beat --loglevel=info

# Optional: Start Flower monitoring (in separate terminal)
celery -A app.tasks.celery_app flower --port=5555
```

### 5. Start Main Application
```bash
# Start FastAPI application with WebSocket support
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## 🧪 Testing the New Features

### 1. WebSocket Connection Test
```javascript
// Frontend WebSocket test
const ws = new WebSocket('ws://localhost:8000/api/v1/websocket/ws?token=YOUR_JWT_TOKEN');

ws.onopen = function(event) {
    console.log('WebSocket connected');
    
    // Subscribe to events
    ws.send(JSON.stringify({
        type: 'subscribe',
        data: {
            event_types: ['threat_detected', 'visitor_connected'],
            website_ids: ['your-website-id']
        }
    }));
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Received event:', data);
};
```

### 2. Test Background Tasks
```bash
# Test scan task
curl -X POST "http://localhost:8000/api/v1/scans/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "website_id": "your-website-id",
    "scan_type": "url_discovery",
    "priority": "normal"
  }'

# Monitor task progress at: http://localhost:5555 (Flower)
```

### 3. Test ML Threat Detection
```bash
# Test threat detection endpoint
curl -X POST "http://localhost:8000/api/v1/websocket/events/test/threat" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "website_id": "your-website-id",
    "threat_type": "sql_injection",
    "severity": "high"
  }'
```

### 4. Test IP Intelligence
```bash
# Test geolocation endpoint (if you create one)
curl -X GET "http://localhost:8000/api/v1/visitors/your-visitor-id" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Check visitor data includes geolocation intelligence
```

## 📊 Monitoring and Maintenance

### 1. Celery Monitoring
- **Flower Dashboard**: http://localhost:5555
- **Task Status**: Monitor task execution and failures
- **Worker Health**: Check worker status and performance

### 2. WebSocket Monitoring
```bash
# Check WebSocket statistics
curl -X GET "http://localhost:8000/api/v1/websocket/stats" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. ML Model Monitoring
- **Model Performance**: Check prediction accuracy
- **Training Status**: Monitor automatic retraining
- **Feature Importance**: Analyze threat detection features

### 4. System Health Checks
```bash
# Run system health check task
celery -A app.tasks.celery_app call app.tasks.maintenance_tasks.system_health_check_task
```

## 🔧 Configuration Tuning

### WebSocket Performance
```python
# In app/core/config.py
WEBSOCKET_MAX_CONNECTIONS = 1000  # Adjust based on server capacity
WEBSOCKET_HEARTBEAT_INTERVAL = 30  # Seconds between heartbeats
WEBSOCKET_CLEANUP_INTERVAL = 300   # Seconds between cleanup runs
```

### Celery Performance
```python
# In app/tasks/celery_app.py
worker_prefetch_multiplier = 1      # Tasks per worker
worker_max_tasks_per_child = 1000   # Tasks before worker restart
task_soft_time_limit = 300          # Soft timeout (seconds)
task_time_limit = 600               # Hard timeout (seconds)
```

### ML Model Performance
```python
# In app/ml/models.py
# Adjust model parameters based on your data:
IsolationForest(contamination=0.1, n_estimators=100)
RandomForestClassifier(n_estimators=100, max_depth=10)
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Redis Connection Error
```bash
# Check Redis status
redis-cli ping

# Restart Redis
sudo systemctl restart redis-server
```

#### 2. Celery Worker Not Starting
```bash
# Check Celery configuration
celery -A app.tasks.celery_app inspect active

# Clear Redis queues
redis-cli FLUSHALL
```

#### 3. WebSocket Connection Fails
- Check JWT token validity
- Verify WebSocket URL format
- Check firewall/proxy settings

#### 4. ML Model Training Fails
- Check available memory (requires 2GB+)
- Verify training data availability
- Check model file permissions

### Performance Optimization

#### 1. Database Optimization
```sql
-- Add indexes for new queries
CREATE INDEX IF NOT EXISTS idx_threats_detected_at ON threats(detected_at);
CREATE INDEX IF NOT EXISTS idx_visitors_created_at ON visitors(created_at);
CREATE INDEX IF NOT EXISTS idx_scans_status ON scans(status);
```

#### 2. Redis Optimization
```bash
# In redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

#### 3. Celery Queue Optimization
```python
# Separate queues for different priorities
CELERY_ROUTES = {
    'app.tasks.scan_tasks.*': {'queue': 'scans'},
    'app.tasks.threat_tasks.*': {'queue': 'threats'},
    'app.tasks.notification_tasks.*': {'queue': 'notifications'},
}
```

## 🎉 Success Verification

After deployment, verify all features are working:

1. ✅ **WebSocket**: Real-time events in browser console
2. ✅ **Background Tasks**: Tasks visible in Flower dashboard
3. ✅ **ML Detection**: Threats detected with confidence scores
4. ✅ **IP Intelligence**: Visitor records include geolocation data
5. ✅ **System Health**: All health checks pass

## 📞 Support

For issues with the advanced features:

1. Check logs: `docker-compose logs -f`
2. Monitor Celery: http://localhost:5555
3. Check Redis: `redis-cli monitor`
4. Review WebSocket connections: `/api/v1/websocket/stats`

The ProSecurity Monitor Backend is now equipped with **enterprise-grade advanced features** ready for production deployment! 🚀
