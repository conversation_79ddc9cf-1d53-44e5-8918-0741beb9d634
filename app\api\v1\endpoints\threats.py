"""
Threat detection and management endpoints
"""

from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional
from uuid import UUID

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.schemas.threat import (
    ThreatCreate, ThreatResponse, ThreatDetailResponse, ThreatListResponse,
    ThreatAnalytics, ThreatRealTimeData, ThreatFilter, ThreatBulkAction
)
from app.services.threat_service import ThreatService

router = APIRouter()
threat_service = ThreatService()


@router.get("/website/{website_id}", response_model=ThreatListResponse)
async def list_website_threats(
    website_id: UUID,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    threat_type: Optional[str] = Query(None, description="Filter by threat type"),
    severity: Optional[str] = Query(None, description="Filter by severity"),
    is_blocked: Optional[bool] = Query(None, description="Filter by blocked status"),
    is_false_positive: Optional[bool] = Query(None, description="Filter by false positive status"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List threats for a specific website with filtering"""
    filters = ThreatFilter(
        threat_type=threat_type,
        severity=severity,
        is_blocked=is_blocked,
        is_false_positive=is_false_positive
    )

    return await threat_service.get_website_threats(
        db,
        website_id=website_id,
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        filters=filters
    )


@router.get("/{threat_id}", response_model=ThreatDetailResponse)
async def get_threat_detail(
    threat_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed threat information"""
    return await threat_service.get_threat_detail(
        db,
        threat_id=threat_id,
        user_id=current_user.id
    )


@router.get("/website/{website_id}/analytics", response_model=ThreatAnalytics)
async def get_threat_analytics(
    website_id: UUID,
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get threat analytics for a website"""
    return await threat_service.get_threat_analytics(
        db,
        website_id=website_id,
        user_id=current_user.id,
        days=days
    )


@router.get("/website/{website_id}/realtime", response_model=ThreatRealTimeData)
async def get_realtime_threats(
    website_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get real-time threat data for a website"""
    return await threat_service.get_realtime_threats(
        db,
        website_id=website_id,
        user_id=current_user.id
    )


@router.post("/{threat_id}/resolve", response_model=ThreatResponse)
async def resolve_threat(
    threat_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Resolve a threat"""
    threat = await threat_service.resolve_threat(
        db,
        threat_id=threat_id,
        user_id=current_user.id
    )
    return ThreatResponse.from_orm(threat)


@router.post("/{threat_id}/false-positive", response_model=ThreatResponse)
async def mark_false_positive(
    threat_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mark threat as false positive"""
    threat = await threat_service.mark_false_positive(
        db,
        threat_id=threat_id,
        user_id=current_user.id
    )
    return ThreatResponse.from_orm(threat)


@router.post("/bulk-action")
async def bulk_action_threats(
    bulk_action: ThreatBulkAction,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Perform bulk action on multiple threats"""
    return await threat_service.bulk_action_threats(
        db,
        bulk_action=bulk_action,
        user_id=current_user.id
    )


@router.post("/", response_model=ThreatResponse, status_code=status.HTTP_201_CREATED)
async def create_threat(
    threat_data: ThreatCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new threat record (typically called by detection systems)"""
    threat = await threat_service.create_threat(
        db,
        threat_data=threat_data
    )
    return ThreatResponse.from_orm(threat)


@router.post("/detect")
async def detect_threats(
    website_id: UUID,
    request_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Detect threats in request data"""
    threats = await threat_service.detect_threats(
        db,
        website_id=website_id,
        request_data=request_data
    )
    return {
        "threats_detected": len(threats),
        "threats": [ThreatResponse.from_orm(t) for t in threats]
    }
