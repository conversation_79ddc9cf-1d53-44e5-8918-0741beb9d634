# GitHub Actions CI/CD Pipeline for Railway Deployment
name: Deploy to Railway

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  PYTHON_VERSION: '3.11'

jobs:
  # Test Job
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-asyncio pytest-cov

    - name: Set environment variables for testing
      run: |
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_db" >> $GITHUB_ENV
        echo "REDIS_URL=redis://localhost:6379/0" >> $GITHUB_ENV
        echo "SECRET_KEY=test-secret-key" >> $GITHUB_ENV
        echo "ENVIRONMENT=testing" >> $GITHUB_ENV

    - name: Run tests
      run: |
        pytest tests/ -v --cov=app --cov-report=xml --cov-report=html

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  # Security Scan
  security:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety

    - name: Run Bandit security scan
      run: bandit -r app/ -f json -o bandit-report.json

    - name: Run Safety check
      run: safety check --json --output safety-report.json

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  # Build and Deploy to Railway
  deploy:
    needs: [test, security]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Railway CLI
      run: |
        curl -fsSL https://railway.app/install.sh | sh
        echo "$HOME/.railway/bin" >> $GITHUB_PATH

    - name: Deploy to Railway
      run: |
        railway login --token ${{ secrets.RAILWAY_TOKEN }}
        railway up --service ${{ secrets.RAILWAY_SERVICE_ID }}
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}

  # Deploy Preview for Pull Requests
  deploy-preview:
    needs: [test, security]
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Railway CLI
      run: |
        curl -fsSL https://railway.app/install.sh | sh
        echo "$HOME/.railway/bin" >> $GITHUB_PATH

    - name: Deploy Preview to Railway
      run: |
        railway login --token ${{ secrets.RAILWAY_TOKEN }}
        railway up --service ${{ secrets.RAILWAY_PREVIEW_SERVICE_ID }}
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}

    - name: Comment PR with preview URL
      uses: actions/github-script@v6
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '🚀 Preview deployment is ready! Check it out at your Railway preview URL.'
          })
