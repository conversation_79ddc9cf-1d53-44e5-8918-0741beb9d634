# ProSecurity Monitor - Development Environment
version: '3.8'

services:
  # FastAPI Backend
  backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - DATABASE_URL=**************************************/prosecurity_monitor
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-change-in-production
    volumes:
      - .:/app
      - /app/venv  # Exclude venv from volume mount
    depends_on:
      - db
      - redis
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    networks:
      - prosecurity-network

  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=prosecurity_monitor
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - prosecurity-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - prosecurity-network

  # Celery Worker for Background Tasks
  celery-worker:
    build: .
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=**************************************/prosecurity_monitor
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-change-in-production
    volumes:
      - .:/app
      - /app/venv
    depends_on:
      - db
      - redis
    command: celery -A app.tasks.celery_app worker --loglevel=info
    networks:
      - prosecurity-network

  # Celery Beat for Scheduled Tasks
  celery-beat:
    build: .
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=**************************************/prosecurity_monitor
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-change-in-production
    volumes:
      - .:/app
      - /app/venv
    depends_on:
      - db
      - redis
    command: celery -A app.tasks.celery_app beat --loglevel=info
    networks:
      - prosecurity-network

  # Flower for Celery Monitoring
  flower:
    build: .
    ports:
      - "5555:5555"
    environment:
      - ENVIRONMENT=development
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - .:/app
    depends_on:
      - redis
    command: celery -A app.tasks.celery_app flower --port=5555
    networks:
      - prosecurity-network

volumes:
  postgres_data:
  redis_data:

networks:
  prosecurity-network:
    driver: bridge
