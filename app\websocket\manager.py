"""
WebSocket connection manager for real-time features
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional, Any
from uuid import UUID, uuid4
from collections import defaultdict, deque

from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from app.websocket.events import (
    WebSocketEvent, EventType, EventSubscription, EventFilter,
    HeartbeatEvent, ConnectionEvent
)

logger = logging.getLogger(__name__)


class WebSocketConnection:
    """Individual WebSocket connection wrapper"""
    
    def __init__(self, websocket: WebSocket, user_id: UUID, connection_id: str):
        self.websocket = websocket
        self.user_id = user_id
        self.connection_id = connection_id
        self.connected_at = datetime.utcnow()
        self.last_activity = datetime.utcnow()
        self.subscriptions: Dict[str, EventSubscription] = {}
        self.is_alive = True
        
    async def send_event(self, event: WebSocketEvent):
        """Send event to this connection"""
        try:
            # Check if any subscription matches this event
            should_send = False
            for subscription in self.subscriptions.values():
                if subscription.matches_event(event):
                    should_send = True
                    break
            
            if should_send or not self.subscriptions:  # Send if no specific subscriptions
                await self.websocket.send_text(event.json())
                self.last_activity = datetime.utcnow()
                
        except Exception as e:
            logger.error(f"Error sending event to connection {self.connection_id}: {e}")
            self.is_alive = False
    
    async def send_json(self, data: Dict[str, Any]):
        """Send JSON data to this connection"""
        try:
            await self.websocket.send_json(data)
            self.last_activity = datetime.utcnow()
        except Exception as e:
            logger.error(f"Error sending JSON to connection {self.connection_id}: {e}")
            self.is_alive = False
    
    def add_subscription(self, subscription: EventSubscription):
        """Add event subscription"""
        self.subscriptions[subscription.subscription_id] = subscription
    
    def remove_subscription(self, subscription_id: str):
        """Remove event subscription"""
        self.subscriptions.pop(subscription_id, None)
    
    def is_stale(self, timeout_minutes: int = 30) -> bool:
        """Check if connection is stale"""
        return datetime.utcnow() - self.last_activity > timedelta(minutes=timeout_minutes)


class ConnectionManager:
    """WebSocket connection manager"""
    
    def __init__(self):
        # Active connections by connection ID
        self.connections: Dict[str, WebSocketConnection] = {}
        
        # Connections by user ID for efficient user-based broadcasting
        self.user_connections: Dict[UUID, Set[str]] = defaultdict(set)
        
        # Connections by website ID for website-specific events
        self.website_connections: Dict[UUID, Set[str]] = defaultdict(set)
        
        # Event history for new connections
        self.event_history: deque = deque(maxlen=1000)
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._heartbeat_task: Optional[asyncio.Task] = None
        
        # Statistics
        self.total_connections = 0
        self.total_events_sent = 0
        
    async def connect(self, websocket: WebSocket, user_id: UUID) -> str:
        """Accept new WebSocket connection"""
        await websocket.accept()
        
        connection_id = str(uuid4())
        connection = WebSocketConnection(websocket, user_id, connection_id)
        
        # Store connection
        self.connections[connection_id] = connection
        self.user_connections[user_id].add(connection_id)
        
        # Update statistics
        self.total_connections += 1
        
        # Send connection established event
        connection_event = ConnectionEvent.create(
            event_type=EventType.CONNECTION_ESTABLISHED,
            connection_id=connection_id,
            client_info={
                "user_id": str(user_id),
                "connected_at": connection.connected_at.isoformat(),
                "total_connections": len(self.connections)
            },
            user_id=user_id
        )
        
        await connection.send_event(connection_event)
        
        # Start background tasks if not running
        if not self._cleanup_task:
            self._cleanup_task = asyncio.create_task(self._cleanup_stale_connections())
        if not self._heartbeat_task:
            self._heartbeat_task = asyncio.create_task(self._send_heartbeats())
        
        logger.info(f"WebSocket connection established: {connection_id} for user {user_id}")
        return connection_id
    
    async def disconnect(self, connection_id: str):
        """Handle WebSocket disconnection"""
        if connection_id not in self.connections:
            return
        
        connection = self.connections[connection_id]
        user_id = connection.user_id
        
        # Remove from all tracking structures
        del self.connections[connection_id]
        self.user_connections[user_id].discard(connection_id)
        
        # Remove from website connections
        for website_id in list(self.website_connections.keys()):
            self.website_connections[website_id].discard(connection_id)
            if not self.website_connections[website_id]:
                del self.website_connections[website_id]
        
        # Clean up empty user connection sets
        if not self.user_connections[user_id]:
            del self.user_connections[user_id]
        
        logger.info(f"WebSocket connection closed: {connection_id} for user {user_id}")
    
    async def subscribe_to_website(self, connection_id: str, website_id: UUID):
        """Subscribe connection to website-specific events"""
        if connection_id in self.connections:
            self.website_connections[website_id].add(connection_id)
    
    async def unsubscribe_from_website(self, connection_id: str, website_id: UUID):
        """Unsubscribe connection from website-specific events"""
        self.website_connections[website_id].discard(connection_id)
        if not self.website_connections[website_id]:
            del self.website_connections[website_id]
    
    async def add_subscription(self, connection_id: str, subscription: EventSubscription):
        """Add event subscription to connection"""
        if connection_id in self.connections:
            self.connections[connection_id].add_subscription(subscription)
    
    async def remove_subscription(self, connection_id: str, subscription_id: str):
        """Remove event subscription from connection"""
        if connection_id in self.connections:
            self.connections[connection_id].remove_subscription(subscription_id)
    
    async def broadcast_event(self, event: WebSocketEvent):
        """Broadcast event to all relevant connections"""
        self.event_history.append(event)
        
        # Determine target connections
        target_connections = set()
        
        # If event has website_id, send to website subscribers
        if event.website_id:
            target_connections.update(self.website_connections.get(event.website_id, set()))
        
        # If event has user_id, send to user connections
        if event.user_id:
            target_connections.update(self.user_connections.get(event.user_id, set()))
        
        # If no specific targeting, send to all connections
        if not target_connections and not event.website_id and not event.user_id:
            target_connections = set(self.connections.keys())
        
        # Send to target connections
        disconnected_connections = []
        for connection_id in target_connections:
            if connection_id in self.connections:
                connection = self.connections[connection_id]
                try:
                    await connection.send_event(event)
                    self.total_events_sent += 1
                except Exception as e:
                    logger.error(f"Failed to send event to {connection_id}: {e}")
                    disconnected_connections.append(connection_id)
        
        # Clean up failed connections
        for connection_id in disconnected_connections:
            await self.disconnect(connection_id)
    
    async def send_to_user(self, user_id: UUID, event: WebSocketEvent):
        """Send event to specific user's connections"""
        event.user_id = user_id
        await self.broadcast_event(event)
    
    async def send_to_website_subscribers(self, website_id: UUID, event: WebSocketEvent):
        """Send event to website subscribers"""
        event.website_id = website_id
        await self.broadcast_event(event)
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            "active_connections": len(self.connections),
            "total_connections": self.total_connections,
            "total_events_sent": self.total_events_sent,
            "connections_by_user": {
                str(user_id): len(connections) 
                for user_id, connections in self.user_connections.items()
            },
            "website_subscriptions": {
                str(website_id): len(connections)
                for website_id, connections in self.website_connections.items()
            }
        }
    
    async def get_recent_events(self, limit: int = 50) -> List[WebSocketEvent]:
        """Get recent events from history"""
        return list(self.event_history)[-limit:]
    
    async def _cleanup_stale_connections(self):
        """Background task to clean up stale connections"""
        while True:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes
                
                stale_connections = []
                for connection_id, connection in self.connections.items():
                    if connection.is_stale() or not connection.is_alive:
                        stale_connections.append(connection_id)
                
                for connection_id in stale_connections:
                    await self.disconnect(connection_id)
                    logger.info(f"Cleaned up stale connection: {connection_id}")
                    
            except Exception as e:
                logger.error(f"Error in cleanup task: {e}")
    
    async def _send_heartbeats(self):
        """Background task to send heartbeat events"""
        while True:
            try:
                await asyncio.sleep(30)  # Send heartbeat every 30 seconds
                
                if self.connections:
                    heartbeat = HeartbeatEvent.create(len(self.connections))
                    await self.broadcast_event(heartbeat)
                    
            except Exception as e:
                logger.error(f"Error in heartbeat task: {e}")


# Global connection manager instance
websocket_manager = ConnectionManager()
