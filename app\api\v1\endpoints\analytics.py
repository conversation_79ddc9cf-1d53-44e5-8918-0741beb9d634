"""
Analytics and reporting endpoints
"""

from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from typing import Optional
from uuid import UUID

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.schemas.analytics import (
    DashboardMetrics, GeographicAnalytics, RealtimeAnalytics,
    AnalyticsReport, AnalyticsQuery
)
from app.services.analytics_service import AnalyticsService

router = APIRouter()
analytics_service = AnalyticsService()


@router.get("/dashboard", response_model=DashboardMetrics)
async def get_dashboard_analytics(
    website_id: Optional[UUID] = Query(None, description="Filter by specific website"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get comprehensive dashboard analytics data"""
    return await analytics_service.get_dashboard_metrics(
        db,
        user_id=current_user.id,
        website_id=website_id
    )


@router.get("/geographic", response_model=GeographicAnalytics)
async def get_geographic_analytics(
    website_id: Optional[UUID] = Query(None, description="Filter by specific website"),
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get geographic analytics data"""
    return await analytics_service.get_geographic_analytics(
        db,
        user_id=current_user.id,
        website_id=website_id,
        days=days
    )


@router.get("/realtime", response_model=RealtimeAnalytics)
async def get_realtime_analytics(
    website_id: Optional[UUID] = Query(None, description="Filter by specific website"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get real-time analytics data"""
    return await analytics_service.get_realtime_analytics(
        db,
        user_id=current_user.id,
        website_id=website_id
    )


@router.post("/reports", response_model=AnalyticsReport)
async def generate_analytics_report(
    website_id: Optional[UUID] = None,
    report_type: str = Query("comprehensive", description="Type of report to generate"),
    days: int = Query(30, ge=1, le=365, description="Number of days to include"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate comprehensive analytics report"""
    return await analytics_service.generate_report(
        db,
        user_id=current_user.id,
        website_id=website_id,
        report_type=report_type,
        days=days
    )
