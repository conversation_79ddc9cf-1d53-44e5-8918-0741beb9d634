# ProSecurity Monitor - Environment Variables Template
# Copy this file to .env and fill in your values

# Application Settings
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-super-secret-key-change-in-production

# Database Configuration
# For Railway: These will be automatically set by Railway
DATABASE_URL=postgresql://user:password@localhost:5432/prosecurity_monitor
DATABASE_PRIVATE_URL=postgresql://user:password@localhost:5432/prosecurity_monitor

# Redis Configuration
# For Railway: These will be automatically set by Railway
REDIS_URL=redis://localhost:6379/0
REDIS_PRIVATE_URL=redis://localhost:6379/0

# CORS Settings (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,https://prosecurity-monitor.vercel.app
ALLOWED_HOSTS=localhost,127.0.0.1,*.railway.app

# External API Keys
GEOLOCATION_API_KEY=your-geolocation-api-key
THREAT_INTELLIGENCE_API_KEY=your-threat-intelligence-api-key

# Monitoring
SENTRY_DSN=your-sentry-dsn-for-error-tracking

# Railway Specific (automatically set by Railway)
RAILWAY_ENVIRONMENT=production
PORT=8000
