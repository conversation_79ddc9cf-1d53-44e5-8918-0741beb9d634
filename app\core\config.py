"""
Configuration management for ProSecurity Monitor
Optimized for Railway deployment with environment variables
"""

import os
from typing import List, Optional
from pydantic import field_validator
from pydantic_settings import BaseSettings
from functools import lru_cache


class Settings(BaseSettings):
    """Application settings with Railway deployment support"""
    
    # Application
    APP_NAME: str = "ProSecurity Monitor"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = os.getenv("RAILWAY_ENVIRONMENT", "development")
    DEBUG: bool = ENVIRONMENT != "production"
    
    # Server Configuration
    HOST: str = "0.0.0.0"
    PORT: int = int(os.getenv("PORT", 8000))  # Railway sets PORT automatically
    
    # Security
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Database Configuration (Railway PostgreSQL)
    DATABASE_URL: Optional[str] = os.getenv("DATABASE_URL")
    DATABASE_PRIVATE_URL: Optional[str] = os.getenv("DATABASE_PRIVATE_URL")  # Railway private network
    
    # Use private URL if available (better performance on Railway)
    @property
    def DB_URL(self) -> str:
        if self.DATABASE_PRIVATE_URL:
            return self.DATABASE_PRIVATE_URL
        elif self.DATABASE_URL:
            return self.DATABASE_URL
        else:
            # Fallback for local development
            return "postgresql://user:password@localhost:5432/prosecurity_monitor"
    
    # Redis Configuration (Railway Redis)
    REDIS_URL: Optional[str] = os.getenv("REDIS_URL")
    REDIS_PRIVATE_URL: Optional[str] = os.getenv("REDIS_PRIVATE_URL")
    
    @property
    def REDIS_CONNECTION_URL(self) -> str:
        if self.REDIS_PRIVATE_URL:
            return self.REDIS_PRIVATE_URL
        elif self.REDIS_URL:
            return self.REDIS_URL
        else:
            # Fallback for local development
            return "redis://localhost:6379/0"
    
    # CORS Settings
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",  # Local frontend
        "https://prosecurity-monitor.vercel.app",  # Production frontend
        "https://*.railway.app",  # Railway preview deployments
    ]
    
    ALLOWED_HOSTS: List[str] = [
        "localhost",
        "127.0.0.1",
        "*.railway.app",  # Railway domains
        "prosecurity-monitor-backend.railway.app",  # Your Railway domain
    ]
    
    # External Services
    GEOLOCATION_API_KEY: Optional[str] = os.getenv("GEOLOCATION_API_KEY")
    THREAT_INTELLIGENCE_API_KEY: Optional[str] = os.getenv("THREAT_INTELLIGENCE_API_KEY")
    
    # Celery Configuration (will be set after initialization)
    CELERY_BROKER_URL: Optional[str] = None
    CELERY_RESULT_BACKEND: Optional[str] = None
    
    # Logging
    LOG_LEVEL: str = "INFO" if ENVIRONMENT == "production" else "DEBUG"
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_BURST: int = 100
    
    # File Upload
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    UPLOAD_DIR: str = "/tmp/uploads"
    
    # Monitoring
    SENTRY_DSN: Optional[str] = os.getenv("SENTRY_DSN")
    
    @field_validator("ALLOWED_ORIGINS", mode="before")
    @classmethod
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v

    @field_validator("ALLOWED_HOSTS", mode="before")
    @classmethod
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    model_config = {
        "env_file": ".env",
        "case_sensitive": True,
        "extra": "ignore"
    }

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Set Celery URLs after initialization
        if not self.CELERY_BROKER_URL:
            self.CELERY_BROKER_URL = self.REDIS_CONNECTION_URL
        if not self.CELERY_RESULT_BACKEND:
            self.CELERY_RESULT_BACKEND = self.REDIS_CONNECTION_URL


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()


# Global settings instance
settings = get_settings()
