# Core FastAPI Dependencies
fastapi>=0.104.0,<0.116.0
uvicorn[standard]>=0.24.0,<0.35.0
pydantic>=2.5.0,<3.0.0
pydantic-settings>=2.0.0,<3.0.0

# Database & ORM
sqlalchemy>=2.0.0,<2.1.0
alembic>=1.13.0,<2.0.0
asyncpg>=0.29.0,<1.0.0
psycopg2-binary>=2.9.0,<3.0.0
redis>=5.0.0,<6.0.0

# Authentication & Security
python-jose[cryptography]>=3.3.0,<4.0.0
passlib[bcrypt]>=1.7.0,<2.0.0
python-multipart>=0.0.6,<1.0.0
cryptography>=41.0.0,<46.0.0

# Background Tasks
celery>=5.3.0,<6.0.0
kombu>=5.3.0,<6.0.0
flower>=2.0.0,<3.0.0

# HTTP Client & Web Scraping
httpx>=0.25.0,<1.0.0
aiohttp>=3.9.0,<4.0.0
beautifulsoup4>=4.12.0,<5.0.0
requests>=2.31.0,<3.0.0

# Machine Learning & Analytics (Optional)
scikit-learn>=1.3.0,<2.0.0
pandas>=2.1.0,<3.0.0
numpy>=1.25.0,<2.0.0

# Monitoring & Logging
prometheus-client>=0.19.0,<1.0.0
structlog>=23.2.0,<25.0.0
sentry-sdk[fastapi]>=1.38.0,<3.0.0

# Geolocation & IP Intelligence
geoip2>=4.7.0,<5.0.0
maxminddb>=2.6.0,<3.0.0

# WebSocket Support
websockets>=12.0,<16.0

# Validation & Utilities
email-validator>=2.1.0,<3.0.0
python-dotenv>=1.0.0,<2.0.0
click>=8.1.0,<9.0.0
typer>=0.9.0,<1.0.0

# Development & Testing
pytest>=7.4.0,<9.0.0
pytest-asyncio>=0.21.0,<1.0.0
pytest-cov>=4.1.0,<7.0.0
black>=23.11.0,<25.0.0
flake8>=6.1.0,<8.0.0
isort>=5.12.0,<6.0.0

# Additional utilities
Pillow>=10.1.0,<12.0.0
python-dateutil>=2.8.0,<3.0.0
pytz>=2023.3
