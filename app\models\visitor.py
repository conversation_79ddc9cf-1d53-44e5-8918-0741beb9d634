"""
Visitor model for tracking website visitors
"""

from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Integer, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID, INET, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from app.core.database import Base


class Visitor(Base):
    __tablename__ = "visitors"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    website_id = Column(UUID(as_uuid=True), ForeignKey("websites.id"), nullable=False)
    
    # Network information
    ip_address = Column(INET, nullable=False, index=True)
    mac_address = Column(String(17), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # Geographic information
    country = Column(String(2), nullable=True, index=True)
    region = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True)
    latitude = Column(String(20), nullable=True)
    longitude = Column(String(20), nullable=True)
    timezone = Column(String(50), nullable=True)
    
    # ISP and network details
    isp = Column(String(255), nullable=True)
    organization = Column(String(255), nullable=True)
    asn = Column(String(20), nullable=True)
    
    # Device information
    device_type = Column(String(50), nullable=True)  # desktop, mobile, tablet
    browser = Column(String(100), nullable=True)
    browser_version = Column(String(50), nullable=True)
    os = Column(String(100), nullable=True)
    os_version = Column(String(50), nullable=True)
    
    # Visit statistics
    first_visit = Column(DateTime(timezone=True), server_default=func.now())
    last_visit = Column(DateTime(timezone=True), server_default=func.now())
    total_visits = Column(Integer, default=1, nullable=False)
    total_pages = Column(Integer, default=1, nullable=False)
    total_time_spent = Column(Integer, default=0, nullable=False)  # seconds
    
    # Security and risk assessment
    is_blocked = Column(Boolean, default=False, nullable=False)
    risk_level = Column(String(20), default="low", nullable=False)  # low, medium, high, critical
    threat_score = Column(Integer, default=0, nullable=False)
    
    # VPN/Proxy detection
    is_vpn = Column(Boolean, default=False, nullable=False)
    is_proxy = Column(Boolean, default=False, nullable=False)
    is_tor = Column(Boolean, default=False, nullable=False)
    
    # Additional data as JSON
    visitor_metadata = Column(JSONB, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    website = relationship("Website", back_populates="visitors")
    threats = relationship("Threat", back_populates="visitor", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Visitor(ip='{self.ip_address}', country='{self.country}')>"
