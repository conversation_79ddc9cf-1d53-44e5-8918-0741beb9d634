"""
Base service class with common functionality
"""

from typing import Generic, TypeVar, Type, Optional, List, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
from pydantic import BaseModel

from app.core.database import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class BaseService(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """Base service class with CRUD operations"""
    
    def __init__(self, model: Type[ModelType]):
        self.model = model
    
    async def create(
        self,
        db: Session,
        *,
        obj_in: CreateSchemaType,
        **kwargs
    ) -> ModelType:
        """Create a new record"""
        try:
            obj_data = obj_in.dict() if hasattr(obj_in, 'dict') else obj_in.model_dump()
            obj_data.update(kwargs)
            db_obj = self.model(**obj_data)
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            return db_obj
        except IntegrityError as e:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Database integrity error: {str(e)}"
            )
        except Exception as e:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating record: {str(e)}"
            )
    
    async def get(self, db: Session, id: UUID) -> Optional[ModelType]:
        """Get a record by ID"""
        return db.query(self.model).filter(self.model.id == id).first()
    
    async def get_or_404(self, db: Session, id: UUID) -> ModelType:
        """Get a record by ID or raise 404"""
        obj = await self.get(db, id)
        if not obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"{self.model.__name__} not found"
            )
        return obj
    
    async def get_multi(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None
    ) -> List[ModelType]:
        """Get multiple records with pagination and filtering"""
        query = db.query(self.model)
        
        # Apply filters
        if filters:
            for key, value in filters.items():
                if hasattr(self.model, key) and value is not None:
                    query = query.filter(getattr(self.model, key) == value)
        
        # Apply ordering
        if order_by and hasattr(self.model, order_by):
            query = query.order_by(getattr(self.model, order_by))
        
        return query.offset(skip).limit(limit).all()
    
    async def count(
        self,
        db: Session,
        *,
        filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """Count records with optional filtering"""
        query = db.query(self.model)
        
        # Apply filters
        if filters:
            for key, value in filters.items():
                if hasattr(self.model, key) and value is not None:
                    query = query.filter(getattr(self.model, key) == value)
        
        return query.count()
    
    async def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: UpdateSchemaType
    ) -> ModelType:
        """Update a record"""
        try:
            obj_data = obj_in.dict(exclude_unset=True) if hasattr(obj_in, 'dict') else obj_in.model_dump(exclude_unset=True)
            
            for field, value in obj_data.items():
                if hasattr(db_obj, field):
                    setattr(db_obj, field, value)
            
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            return db_obj
        except IntegrityError as e:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Database integrity error: {str(e)}"
            )
        except Exception as e:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error updating record: {str(e)}"
            )
    
    async def delete(self, db: Session, *, id: UUID) -> ModelType:
        """Delete a record by ID"""
        obj = await self.get_or_404(db, id)
        try:
            db.delete(obj)
            db.commit()
            return obj
        except Exception as e:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error deleting record: {str(e)}"
            )
    
    async def bulk_delete(self, db: Session, *, ids: List[UUID]) -> int:
        """Delete multiple records by IDs"""
        try:
            count = db.query(self.model).filter(self.model.id.in_(ids)).count()
            db.query(self.model).filter(self.model.id.in_(ids)).delete(synchronize_session=False)
            db.commit()
            return count
        except Exception as e:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error deleting records: {str(e)}"
            )
    
    async def exists(self, db: Session, *, id: UUID) -> bool:
        """Check if a record exists"""
        return db.query(self.model).filter(self.model.id == id).first() is not None
    
    async def get_by_field(
        self,
        db: Session,
        *,
        field: str,
        value: Any
    ) -> Optional[ModelType]:
        """Get a record by a specific field"""
        if not hasattr(self.model, field):
            raise ValueError(f"Model {self.model.__name__} has no field '{field}'")
        
        return db.query(self.model).filter(getattr(self.model, field) == value).first()
    
    async def get_multi_by_field(
        self,
        db: Session,
        *,
        field: str,
        value: Any,
        skip: int = 0,
        limit: int = 100
    ) -> List[ModelType]:
        """Get multiple records by a specific field"""
        if not hasattr(self.model, field):
            raise ValueError(f"Model {self.model.__name__} has no field '{field}'")
        
        return (
            db.query(self.model)
            .filter(getattr(self.model, field) == value)
            .offset(skip)
            .limit(limit)
            .all()
        )
