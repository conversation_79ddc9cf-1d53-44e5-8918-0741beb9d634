"""
Event service for WebSocket event emission
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from uuid import UUID

from app.websocket.manager import websocket_manager
from app.websocket.events import (
    EventType, VisitorEvent, ThreatEvent, ScanEvent, SystemEvent, AnalyticsEvent
)

logger = logging.getLogger(__name__)


class EventService:
    """Service for emitting WebSocket events from business logic"""
    
    def __init__(self):
        self.enabled = True
    
    async def emit_visitor_event(
        self,
        event_type: EventType,
        visitor_id: UUID,
        website_id: UUID,
        ip_address: str,
        **kwargs
    ):
        """Emit visitor-related event"""
        if not self.enabled:
            return
        
        try:
            event = VisitorEvent.create(
                event_type=event_type,
                visitor_id=visitor_id,
                ip_address=ip_address,
                website_id=website_id,
                **kwargs
            )
            
            await websocket_manager.broadcast_event(event)
            logger.debug(f"Emitted visitor event: {event_type} for visitor {visitor_id}")
            
        except Exception as e:
            logger.error(f"Error emitting visitor event: {e}")
    
    async def emit_threat_event(
        self,
        event_type: EventType,
        threat_id: UUID,
        website_id: UUID,
        threat_type: str,
        severity: str,
        confidence_score: int,
        **kwargs
    ):
        """Emit threat-related event"""
        if not self.enabled:
            return
        
        try:
            event = ThreatEvent.create(
                event_type=event_type,
                threat_id=threat_id,
                threat_type=threat_type,
                severity=severity,
                confidence_score=confidence_score,
                website_id=website_id,
                **kwargs
            )
            
            await websocket_manager.broadcast_event(event)
            logger.debug(f"Emitted threat event: {event_type} for threat {threat_id}")
            
        except Exception as e:
            logger.error(f"Error emitting threat event: {e}")
    
    async def emit_scan_event(
        self,
        event_type: EventType,
        scan_id: UUID,
        website_id: UUID,
        scan_type: str,
        progress: int = 0,
        **kwargs
    ):
        """Emit scan-related event"""
        if not self.enabled:
            return
        
        try:
            event = ScanEvent.create(
                event_type=event_type,
                scan_id=scan_id,
                scan_type=scan_type,
                website_id=website_id,
                progress=progress,
                **kwargs
            )
            
            await websocket_manager.broadcast_event(event)
            logger.debug(f"Emitted scan event: {event_type} for scan {scan_id}")
            
        except Exception as e:
            logger.error(f"Error emitting scan event: {e}")
    
    async def emit_system_event(
        self,
        event_type: EventType,
        message: str,
        alert_level: str = "info",
        website_id: Optional[UUID] = None,
        **kwargs
    ):
        """Emit system-related event"""
        if not self.enabled:
            return
        
        try:
            event = SystemEvent.create(
                event_type=event_type,
                message=message,
                alert_level=alert_level,
                website_id=website_id,
                **kwargs
            )
            
            await websocket_manager.broadcast_event(event)
            logger.debug(f"Emitted system event: {event_type} - {message}")
            
        except Exception as e:
            logger.error(f"Error emitting system event: {e}")
    
    async def emit_analytics_event(
        self,
        event_type: EventType,
        metric_type: str,
        metric_value: Any,
        website_id: Optional[UUID] = None,
        **kwargs
    ):
        """Emit analytics-related event"""
        if not self.enabled:
            return
        
        try:
            event = AnalyticsEvent.create(
                event_type=event_type,
                metric_type=metric_type,
                metric_value=metric_value,
                website_id=website_id,
                **kwargs
            )
            
            await websocket_manager.broadcast_event(event)
            logger.debug(f"Emitted analytics event: {event_type} - {metric_type}: {metric_value}")
            
        except Exception as e:
            logger.error(f"Error emitting analytics event: {e}")
    
    # Convenience methods for common events
    
    async def visitor_connected(self, visitor_id: UUID, website_id: UUID, ip_address: str, **kwargs):
        """Emit visitor connected event"""
        await self.emit_visitor_event(
            EventType.VISITOR_CONNECTED, visitor_id, website_id, ip_address, **kwargs
        )
    
    async def visitor_disconnected(self, visitor_id: UUID, website_id: UUID, ip_address: str, **kwargs):
        """Emit visitor disconnected event"""
        await self.emit_visitor_event(
            EventType.VISITOR_DISCONNECTED, visitor_id, website_id, ip_address, **kwargs
        )
    
    async def visitor_page_view(self, visitor_id: UUID, website_id: UUID, ip_address: str, page_url: str, **kwargs):
        """Emit visitor page view event"""
        await self.emit_visitor_event(
            EventType.VISITOR_PAGE_VIEW, visitor_id, website_id, ip_address, 
            page_url=page_url, **kwargs
        )
    
    async def visitor_blocked(self, visitor_id: UUID, website_id: UUID, ip_address: str, reason: str, **kwargs):
        """Emit visitor blocked event"""
        await self.emit_visitor_event(
            EventType.VISITOR_BLOCKED, visitor_id, website_id, ip_address,
            reason=reason, **kwargs
        )
    
    async def threat_detected(self, threat_id: UUID, website_id: UUID, threat_type: str, 
                            severity: str, confidence_score: int, **kwargs):
        """Emit threat detected event"""
        await self.emit_threat_event(
            EventType.THREAT_DETECTED, threat_id, website_id, threat_type,
            severity, confidence_score, **kwargs
        )
    
    async def threat_blocked(self, threat_id: UUID, website_id: UUID, threat_type: str,
                           severity: str, confidence_score: int, **kwargs):
        """Emit threat blocked event"""
        await self.emit_threat_event(
            EventType.THREAT_BLOCKED, threat_id, website_id, threat_type,
            severity, confidence_score, **kwargs
        )
    
    async def threat_resolved(self, threat_id: UUID, website_id: UUID, threat_type: str,
                            severity: str, confidence_score: int, **kwargs):
        """Emit threat resolved event"""
        await self.emit_threat_event(
            EventType.THREAT_RESOLVED, threat_id, website_id, threat_type,
            severity, confidence_score, **kwargs
        )
    
    async def scan_started(self, scan_id: UUID, website_id: UUID, scan_type: str, **kwargs):
        """Emit scan started event"""
        await self.emit_scan_event(
            EventType.SCAN_STARTED, scan_id, website_id, scan_type, 0, **kwargs
        )
    
    async def scan_progress(self, scan_id: UUID, website_id: UUID, scan_type: str, progress: int, **kwargs):
        """Emit scan progress event"""
        await self.emit_scan_event(
            EventType.SCAN_PROGRESS, scan_id, website_id, scan_type, progress, **kwargs
        )
    
    async def scan_completed(self, scan_id: UUID, website_id: UUID, scan_type: str, **kwargs):
        """Emit scan completed event"""
        await self.emit_scan_event(
            EventType.SCAN_COMPLETED, scan_id, website_id, scan_type, 100, **kwargs
        )
    
    async def scan_failed(self, scan_id: UUID, website_id: UUID, scan_type: str, error: str, **kwargs):
        """Emit scan failed event"""
        await self.emit_scan_event(
            EventType.SCAN_FAILED, scan_id, website_id, scan_type, 0,
            error=error, **kwargs
        )
    
    async def website_down(self, website_id: UUID, url: str, error: str, **kwargs):
        """Emit website down event"""
        await self.emit_system_event(
            EventType.WEBSITE_DOWN, f"Website {url} is down: {error}",
            alert_level="critical", website_id=website_id, url=url, error=error, **kwargs
        )
    
    async def website_up(self, website_id: UUID, url: str, **kwargs):
        """Emit website up event"""
        await self.emit_system_event(
            EventType.WEBSITE_UP, f"Website {url} is back online",
            alert_level="info", website_id=website_id, url=url, **kwargs
        )
    
    async def ssl_expiring(self, website_id: UUID, domain: str, days_until_expiry: int, **kwargs):
        """Emit SSL expiring event"""
        await self.emit_system_event(
            EventType.SSL_EXPIRING, 
            f"SSL certificate for {domain} expires in {days_until_expiry} days",
            alert_level="warning", website_id=website_id, domain=domain,
            days_until_expiry=days_until_expiry, **kwargs
        )
    
    async def dashboard_refresh(self, website_id: Optional[UUID] = None, **kwargs):
        """Emit dashboard refresh event"""
        await self.emit_analytics_event(
            EventType.DASHBOARD_REFRESH, "dashboard", "refresh",
            website_id=website_id, **kwargs
        )
    
    def disable_events(self):
        """Disable event emission (useful for testing)"""
        self.enabled = False
    
    def enable_events(self):
        """Enable event emission"""
        self.enabled = True


# Global event service instance
event_service = EventService()
