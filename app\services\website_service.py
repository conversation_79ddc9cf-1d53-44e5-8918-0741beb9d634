"""
Website service for business logic
"""

import ssl
import socket
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from fastapi import HTT<PERSON>Exception, status
from urllib.parse import urlparse

from app.models.website import Website
from app.models.user import User
from app.schemas.website import (
    WebsiteCreate, WebsiteUpdate, WebsiteResponse, WebsiteDetailResponse,
    WebsiteListResponse, WebsiteHealthCheck, WebsiteVerification,
    WebsiteVerificationResponse, WebsiteStats
)
from app.services.base_service import BaseService


class WebsiteService(BaseService[Website, WebsiteCreate, WebsiteUpdate]):
    """Website service with business logic"""
    
    def __init__(self):
        super().__init__(Website)
    
    async def create_website(
        self,
        db: Session,
        *,
        website_data: WebsiteCreate,
        user: User
    ) -> Website:
        """Create a new website with validation"""
        # Check if user already has a website with this domain
        existing = await self.get_by_domain_and_user(db, website_data.domain, user.id)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Website with this domain already exists for this user"
            )
        
        # Validate website accessibility
        await self._validate_website_accessibility(str(website_data.url))
        
        # Create website
        website = await self.create(
            db,
            obj_in=website_data,
            user_id=user.id
        )
        
        # Trigger initial health check
        asyncio.create_task(self._perform_health_check(website.id, str(website.url)))
        
        return website
    
    async def get_user_websites(
        self,
        db: Session,
        *,
        user_id: UUID,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None
    ) -> WebsiteListResponse:
        """Get websites for a specific user"""
        filters = {"user_id": user_id}
        if is_active is not None:
            filters["is_active"] = is_active
        
        websites = await self.get_multi(
            db,
            skip=skip,
            limit=limit,
            filters=filters,
            order_by="created_at"
        )
        
        total = await self.count(db, filters=filters)
        pages = (total + limit - 1) // limit
        
        return WebsiteListResponse(
            websites=[WebsiteResponse.from_orm(w) for w in websites],
            total=total,
            page=(skip // limit) + 1,
            per_page=limit,
            pages=pages
        )
    
    async def get_website_detail(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID
    ) -> WebsiteDetailResponse:
        """Get detailed website information"""
        website = await self.get_or_404(db, website_id)
        
        # Check ownership
        if website.user_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this website"
            )
        
        # Get statistics
        stats = await self._get_website_stats(db, website_id)
        
        # Convert to detailed response
        detail_data = WebsiteResponse.from_orm(website).dict()
        detail_data.update(stats)
        
        return WebsiteDetailResponse(**detail_data)
    
    async def update_website(
        self,
        db: Session,
        *,
        website_id: UUID,
        website_data: WebsiteUpdate,
        user_id: UUID
    ) -> Website:
        """Update website configuration"""
        website = await self.get_or_404(db, website_id)
        
        # Check ownership
        if website.user_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this website"
            )
        
        return await self.update(db, db_obj=website, obj_in=website_data)
    
    async def delete_website(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID
    ) -> Website:
        """Delete a website"""
        website = await self.get_or_404(db, website_id)
        
        # Check ownership
        if website.user_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to delete this website"
            )
        
        return await self.delete(db, id=website_id)
    
    async def perform_health_check(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID
    ) -> WebsiteHealthCheck:
        """Perform health check on a website"""
        website = await self.get_or_404(db, website_id)
        
        # Check ownership
        if website.user_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to check this website"
            )
        
        return await self._perform_health_check(website_id, website.url)
    
    async def verify_website(
        self,
        db: Session,
        *,
        website_id: UUID,
        verification_data: WebsiteVerification,
        user_id: UUID
    ) -> WebsiteVerificationResponse:
        """Verify website ownership"""
        website = await self.get_or_404(db, website_id)
        
        # Check ownership
        if website.user_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to verify this website"
            )
        
        verified = await self._verify_website_ownership(
            website.url,
            verification_data.verification_method,
            verification_data.verification_token
        )
        
        return WebsiteVerificationResponse(
            verified=verified,
            verification_method=verification_data.verification_method,
            verification_token=verification_data.verification_token,
            verified_at=datetime.utcnow() if verified else None,
            error_message=None if verified else "Verification failed"
        )
    
    async def get_by_domain_and_user(
        self,
        db: Session,
        domain: str,
        user_id: UUID
    ) -> Optional[Website]:
        """Get website by domain and user"""
        return db.query(Website).filter(
            and_(Website.domain == domain, Website.user_id == user_id)
        ).first()
    
    async def _validate_website_accessibility(self, url: str) -> None:
        """Validate that the website is accessible"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get(url) as response:
                    if response.status >= 400:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail=f"Website returned status code {response.status}"
                        )
        except aiohttp.ClientError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Website is not accessible"
            )
        except asyncio.TimeoutError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Website request timed out"
            )
    
    async def _perform_health_check(self, website_id: UUID, url: str) -> WebsiteHealthCheck:
        """Perform comprehensive health check"""
        start_time = datetime.utcnow()
        
        try:
            # HTTP health check
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.get(url) as response:
                    status_code = response.status
                    response_time = (datetime.utcnow() - start_time).total_seconds()
            
            # SSL check
            ssl_valid, ssl_expiry = await self._check_ssl_certificate(url)
            
            # Security headers check
            security_headers = await self._check_security_headers(url)
            
            return WebsiteHealthCheck(
                website_id=website_id,
                status_code=status_code,
                response_time=response_time,
                ssl_valid=ssl_valid,
                ssl_expiry_date=ssl_expiry,
                security_headers=security_headers,
                error_message=None,
                checked_at=datetime.utcnow()
            )
            
        except Exception as e:
            return WebsiteHealthCheck(
                website_id=website_id,
                status_code=0,
                response_time=0.0,
                ssl_valid=False,
                ssl_expiry_date=None,
                security_headers={},
                error_message=str(e),
                checked_at=datetime.utcnow()
            )
    
    async def _check_ssl_certificate(self, url: str) -> Tuple[bool, Optional[datetime]]:
        """Check SSL certificate validity"""
        try:
            parsed_url = urlparse(url)
            if parsed_url.scheme != 'https':
                return False, None
            
            hostname = parsed_url.hostname
            port = parsed_url.port or 443
            
            context = ssl.create_default_context()
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    
                    # Parse expiry date
                    expiry_str = cert['notAfter']
                    expiry_date = datetime.strptime(expiry_str, '%b %d %H:%M:%S %Y %Z')
                    
                    # Check if valid
                    is_valid = expiry_date > datetime.utcnow()
                    
                    return is_valid, expiry_date
                    
        except Exception:
            return False, None
    
    async def _check_security_headers(self, url: str) -> Dict[str, bool]:
        """Check security headers"""
        security_headers = {
            'X-Content-Type-Options': False,
            'X-Frame-Options': False,
            'X-XSS-Protection': False,
            'Strict-Transport-Security': False,
            'Content-Security-Policy': False,
            'Referrer-Policy': False
        }
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get(url) as response:
                    headers = response.headers
                    
                    for header in security_headers:
                        security_headers[header] = header in headers
                        
        except Exception:
            pass
        
        return security_headers
    
    async def _verify_website_ownership(
        self,
        url: str,
        method: str,
        token: str
    ) -> bool:
        """Verify website ownership using different methods"""
        try:
            if method == "meta":
                return await self._verify_meta_tag(url, token)
            elif method == "file":
                return await self._verify_file(url, token)
            elif method == "dns":
                return await self._verify_dns(url, token)
            else:
                return False
        except Exception:
            return False
    
    async def _verify_meta_tag(self, url: str, token: str) -> bool:
        """Verify ownership via meta tag"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    content = await response.text()
                    return f'content="{token}"' in content
        except Exception:
            return False
    
    async def _verify_file(self, url: str, token: str) -> bool:
        """Verify ownership via file"""
        try:
            verification_url = f"{url.rstrip('/')}/prosecurity-verification.txt"
            async with aiohttp.ClientSession() as session:
                async with session.get(verification_url) as response:
                    content = await response.text()
                    return token.strip() in content.strip()
        except Exception:
            return False
    
    async def _verify_dns(self, url: str, token: str) -> bool:
        """Verify ownership via DNS TXT record"""
        # This would require a DNS library like dnspython
        # For now, return False as placeholder
        return False
    
    async def _get_website_stats(self, db: Session, website_id: UUID) -> Dict[str, Any]:
        """Get website statistics"""
        # This would query related tables for statistics
        # For now, return default values
        return {
            "total_scans": 0,
            "total_visitors": 0,
            "total_threats": 0,
            "total_blocking_rules": 0,
            "uptime_percentage": 99.9,
            "avg_response_time": 0.5,
            "ssl_expiry_date": None,
            "last_error": None
        }
