"""
Scan model for URL discovery and database analysis
"""

from sqlalchemy import Column, <PERSON>, <PERSON>ole<PERSON>, DateTime, Integer, ForeignKey, Text, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum

from app.core.database import Base


class ScanType(str, enum.Enum):
    URL_DISCOVERY = "url_discovery"
    DATABASE_ANALYSIS = "database_analysis"
    SECURITY_SCAN = "security_scan"
    VULNERABILITY_SCAN = "vulnerability_scan"


class ScanStatus(str, enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Scan(Base):
    __tablename__ = "scans"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    website_id = Column(UUID(as_uuid=True), ForeignKey("websites.id"), nullable=False)
    
    # Scan configuration
    scan_type = Column(Enum(ScanType), nullable=False)
    status = Column(Enum(ScanStatus), default=ScanStatus.PENDING, nullable=False)
    progress = Column(Integer, default=0, nullable=False)  # 0-100
    
    # Scan parameters
    target_url = Column(String(500), nullable=True)
    max_depth = Column(Integer, default=3, nullable=False)
    max_pages = Column(Integer, default=1000, nullable=False)
    delay_between_requests = Column(Integer, default=1, nullable=False)  # seconds
    
    # Results and metadata
    results = Column(JSONB, nullable=True)
    summary = Column(JSONB, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Statistics
    urls_discovered = Column(Integer, default=0, nullable=False)
    forms_found = Column(Integer, default=0, nullable=False)
    apis_found = Column(Integer, default=0, nullable=False)
    vulnerabilities_found = Column(Integer, default=0, nullable=False)
    
    # Timing
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    duration_seconds = Column(Integer, nullable=True)
    
    # Configuration
    config = Column(JSONB, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    website = relationship("Website", back_populates="scans")
    
    def __repr__(self):
        return f"<Scan(type='{self.scan_type}', status='{self.status}')>"
