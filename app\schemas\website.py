"""
Website schemas for request/response models
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field, HttpUrl, validator
from enum import Enum


class WebsiteStatus(str, Enum):
    """Website status enumeration"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PENDING = "pending"


class WebsiteCreate(BaseModel):
    """Schema for creating a new website"""
    domain: str = Field(..., min_length=1, max_length=255, description="Website domain")
    url: HttpUrl = Field(..., description="Full website URL")
    name: Optional[str] = Field(None, max_length=255, description="Display name for the website")
    description: Optional[str] = Field(None, max_length=1000, description="Website description")
    
    # Configuration options
    scan_frequency: int = Field(3600, ge=300, le=86400, description="Scan frequency in seconds (5min-24h)")
    monitoring_enabled: bool = Field(True, description="Enable monitoring for this website")
    ssl_monitoring: bool = Field(True, description="Enable SSL certificate monitoring")
    security_headers_check: bool = Field(True, description="Enable security headers checking")
    
    # Crawling configuration
    max_crawl_depth: int = Field(3, ge=1, le=10, description="Maximum crawl depth")
    crawl_delay: int = Field(1, ge=0, le=10, description="Delay between requests in seconds")
    respect_robots_txt: bool = Field(True, description="Respect robots.txt file")
    
    # Additional configuration
    config: Optional[Dict[str, Any]] = Field(None, description="Additional configuration as JSON")

    @validator('domain')
    def validate_domain(cls, v):
        """Validate domain format"""
        if not v or len(v.strip()) == 0:
            raise ValueError('Domain cannot be empty')
        # Remove protocol if present
        domain = v.lower().replace('http://', '').replace('https://', '').replace('www.', '')
        # Remove trailing slash
        domain = domain.rstrip('/')
        return domain

    @validator('url')
    def validate_url(cls, v):
        """Validate URL format"""
        if not str(v).startswith(('http://', 'https://')):
            raise ValueError('URL must start with http:// or https://')
        return v


class WebsiteUpdate(BaseModel):
    """Schema for updating a website"""
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    scan_frequency: Optional[int] = Field(None, ge=300, le=86400)
    monitoring_enabled: Optional[bool] = None
    ssl_monitoring: Optional[bool] = None
    security_headers_check: Optional[bool] = None
    max_crawl_depth: Optional[int] = Field(None, ge=1, le=10)
    crawl_delay: Optional[int] = Field(None, ge=0, le=10)
    respect_robots_txt: Optional[bool] = None
    config: Optional[Dict[str, Any]] = None


class WebsiteResponse(BaseModel):
    """Schema for website response"""
    id: UUID
    user_id: UUID
    domain: str
    url: str
    name: Optional[str]
    description: Optional[str]
    is_active: bool
    scan_frequency: int
    monitoring_enabled: bool
    ssl_monitoring: bool
    security_headers_check: bool
    max_crawl_depth: int
    crawl_delay: int
    respect_robots_txt: bool
    config: Optional[Dict[str, Any]]
    last_scan_at: Optional[datetime]
    last_health_check: Optional[datetime]
    status: str
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class WebsiteDetailResponse(WebsiteResponse):
    """Detailed website response with additional statistics"""
    total_scans: int = 0
    total_visitors: int = 0
    total_threats: int = 0
    total_blocking_rules: int = 0
    uptime_percentage: float = 0.0
    avg_response_time: float = 0.0
    ssl_expiry_date: Optional[datetime] = None
    last_error: Optional[str] = None


class WebsiteListResponse(BaseModel):
    """Response for website listing with pagination"""
    websites: List[WebsiteResponse]
    total: int
    page: int
    per_page: int
    pages: int


class WebsiteHealthCheck(BaseModel):
    """Website health check result"""
    website_id: UUID
    status_code: int
    response_time: float
    ssl_valid: bool
    ssl_expiry_date: Optional[datetime]
    security_headers: Dict[str, bool]
    error_message: Optional[str]
    checked_at: datetime


class WebsiteVerification(BaseModel):
    """Website verification request"""
    verification_method: str = Field(..., description="Verification method: dns, file, meta")
    verification_token: str = Field(..., description="Verification token to check")


class WebsiteVerificationResponse(BaseModel):
    """Website verification response"""
    verified: bool
    verification_method: str
    verification_token: str
    verified_at: Optional[datetime]
    error_message: Optional[str]


class WebsiteScanTrigger(BaseModel):
    """Schema for triggering a website scan"""
    scan_type: str = Field(..., description="Type of scan: url_discovery, security, full")
    priority: str = Field("normal", description="Scan priority: low, normal, high")
    config: Optional[Dict[str, Any]] = Field(None, description="Scan-specific configuration")


class WebsiteStats(BaseModel):
    """Website statistics"""
    total_pages: int = 0
    total_forms: int = 0
    total_apis: int = 0
    total_vulnerabilities: int = 0
    security_score: float = 0.0
    performance_score: float = 0.0
    last_updated: Optional[datetime] = None
