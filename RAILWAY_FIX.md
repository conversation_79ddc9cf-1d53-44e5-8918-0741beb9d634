# Railway Deployment Fix - PORT Environment Variable Issue

## 🐛 Problem
Railway deployment was failing with the error:
```
Error: Invalid value for '--port': '$PORT' is not a valid integer.
```

This happened because the `$PORT` environment variable wasn't being properly expanded in the Docker CMD instruction.

## ✅ Solution Applied

### 1. Created Custom Server Runner (`run_server.py`)
- Handles PORT environment variable properly in Python
- Provides fallback to port 8000 if PORT is not set or invalid
- Includes proper error handling and logging

### 2. Updated Dockerfile
- Removed problematic shell variable expansion
- Now uses Python script for reliable PORT handling
- Simplified command: `CMD ["python", "run_server.py"]`

### 3. Simplified railway.json
- Removed custom startCommand that was causing issues
- Let Docker handle the startup process
- Increased healthcheck timeout for better reliability

## 🚀 How to Deploy to Railway

### Step 1: Commit and Push Changes
```bash
git add .
git commit -m "Fix Railway PORT environment variable handling"
git push origin main
```

### Step 2: Deploy to Railway
```bash
# If you haven't already:
railway login
railway init

# Add required services
railway add postgresql
railway add redis

# Deploy
railway up
```

### Step 3: Set Environment Variables (Optional)
In Railway dashboard, add:
- `SECRET_KEY` - Your JWT secret key
- `ENVIRONMENT` - Set to "production"
- `ALLOWED_ORIGINS` - Your frontend domain

### Step 4: Monitor Deployment
```bash
# Check status
railway status

# View logs
railway logs

# Check health
curl https://your-app.railway.app/health
```

## 🔧 Technical Details

### Before (Problematic)
```dockerfile
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "$PORT"]
```
❌ Shell variable `$PORT` not expanded properly

### After (Fixed)
```dockerfile
CMD ["python", "run_server.py"]
```
✅ Python script handles PORT environment variable correctly

### Server Runner Logic
```python
port = 8000
port_env = os.getenv("PORT")

if port_env:
    try:
        port = int(port_env)
        print(f"🚀 Using PORT from environment: {port}")
    except (ValueError, TypeError):
        print(f"⚠️  Invalid PORT value '{port_env}', using default: {port}")
```

## 🧪 Testing Locally

Test the fix locally:
```bash
# Default port (8000)
python run_server.py

# Custom port
PORT=9000 python run_server.py
```

## 📋 Files Modified

1. **`run_server.py`** - New Python server runner
2. **`Dockerfile`** - Updated CMD instruction
3. **`railway.json`** - Simplified configuration
4. **`start.sh`** - Alternative bash script (backup)

## 🎯 Expected Results

After deployment, you should see:
- ✅ Railway deployment succeeds
- ✅ Application starts on Railway-assigned PORT
- ✅ Health check passes at `/health`
- ✅ API documentation available at `/docs`

## 🆘 Troubleshooting

If deployment still fails:

1. **Check Railway logs:**
   ```bash
   railway logs
   ```

2. **Verify environment variables:**
   ```bash
   railway variables
   ```

3. **Test health endpoint:**
   ```bash
   curl https://your-app.railway.app/health
   ```

4. **Check service status:**
   ```bash
   railway status
   ```

## 🔄 Rollback Plan

If needed, you can rollback to a previous deployment:
```bash
railway rollback [deployment-id]
```

Your ProSecurity Monitor backend should now deploy successfully to Railway! 🚀
