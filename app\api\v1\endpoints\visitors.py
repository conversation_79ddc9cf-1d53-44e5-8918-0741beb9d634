"""
Visitor tracking and analytics endpoints
"""

from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional
from uuid import UUID

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.schemas.visitor import (
    VisitorCreate, VisitorResponse, VisitorDetailResponse, VisitorListResponse,
    VisitorAnalytics, VisitorRealTimeData, VisitorFilter, VisitorBulkAction
)
from app.services.visitor_service import VisitorService

router = APIRouter()
visitor_service = VisitorService()


@router.get("/website/{website_id}", response_model=VisitorListResponse)
async def list_website_visitors(
    website_id: UUID,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    country: Optional[str] = Query(None, description="Filter by country"),
    device_type: Optional[str] = Query(None, description="Filter by device type"),
    risk_level: Optional[str] = Query(None, description="Filter by risk level"),
    is_blocked: Optional[bool] = Query(None, description="Filter by blocked status"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get visitor list for a specific website with filtering"""
    filters = VisitorFilter(
        country=country,
        device_type=device_type,
        risk_level=risk_level,
        is_blocked=is_blocked
    )

    return await visitor_service.get_website_visitors(
        db,
        website_id=website_id,
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        filters=filters
    )


@router.get("/{visitor_id}", response_model=VisitorDetailResponse)
async def get_visitor_detail(
    visitor_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed visitor information"""
    return await visitor_service.get_visitor_detail(
        db,
        visitor_id=visitor_id,
        user_id=current_user.id
    )


@router.get("/website/{website_id}/analytics", response_model=VisitorAnalytics)
async def get_visitor_analytics(
    website_id: UUID,
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get visitor analytics for a website"""
    return await visitor_service.get_visitor_analytics(
        db,
        website_id=website_id,
        user_id=current_user.id,
        days=days
    )


@router.get("/website/{website_id}/realtime", response_model=VisitorRealTimeData)
async def get_realtime_visitors(
    website_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get real-time visitor data for a website"""
    return await visitor_service.get_realtime_visitors(
        db,
        website_id=website_id,
        user_id=current_user.id
    )


@router.post("/{visitor_id}/block", response_model=VisitorResponse)
async def block_visitor(
    visitor_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Block a specific visitor"""
    visitor = await visitor_service.block_visitor(
        db,
        visitor_id=visitor_id,
        user_id=current_user.id
    )
    return VisitorResponse.from_orm(visitor)


@router.post("/bulk-action")
async def bulk_action_visitors(
    bulk_action: VisitorBulkAction,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Perform bulk action on multiple visitors"""
    return await visitor_service.bulk_action_visitors(
        db,
        bulk_action=bulk_action,
        user_id=current_user.id
    )


@router.post("/", response_model=VisitorResponse, status_code=status.HTTP_201_CREATED)
async def create_visitor_record(
    visitor_data: VisitorCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create or update a visitor record (typically called by tracking script)"""
    visitor = await visitor_service.create_visitor(
        db,
        visitor_data=visitor_data
    )
    return VisitorResponse.from_orm(visitor)
