"""
Visitor service for business logic
"""

import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPEx<PERSON>, status
from ipaddress import ip_address, IPv4Address, IPv6Address

from app.models.visitor import Visitor
from app.models.website import Website
from app.schemas.visitor import (
    VisitorCreate, VisitorUpdate, VisitorResponse, VisitorDetailResponse,
    VisitorListResponse, VisitorAnalytics, VisitorGeographicData,
    VisitorRealTimeData, VisitorFilter, VisitorBulkAction
)
from app.services.base_service import BaseService


class VisitorService(BaseService[Visitor, VisitorCreate, VisitorUpdate]):
    """Visitor service with business logic"""
    
    def __init__(self):
        super().__init__(Visitor)
    
    async def create_visitor(
        self,
        db: Session,
        *,
        visitor_data: VisitorCreate
    ) -> Visitor:
        """Create or update visitor record"""
        # Check if visitor already exists for this website and IP
        existing_visitor = await self.get_by_ip_and_website(
            db, visitor_data.ip_address, visitor_data.website_id
        )
        
        if existing_visitor:
            # Update existing visitor
            update_data = VisitorUpdate(
                total_visits=existing_visitor.total_visits + 1,
                total_pages=existing_visitor.total_pages + 1,
                visitor_metadata=visitor_data.visitor_metadata
            )
            existing_visitor.last_visit = datetime.utcnow()
            return await self.update(db, db_obj=existing_visitor, obj_in=update_data)
        else:
            # Enrich visitor data with geolocation and device info
            enriched_data = await self._enrich_visitor_data(visitor_data)
            return await self.create(db, obj_in=enriched_data)
    
    async def get_website_visitors(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[VisitorFilter] = None
    ) -> VisitorListResponse:
        """Get visitors for a specific website"""
        # Verify website ownership
        website = db.query(Website).filter(
            and_(Website.id == website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Website not found or access denied"
            )
        
        # Build query with filters
        query = db.query(Visitor).filter(Visitor.website_id == website_id)
        
        if filters:
            query = self._apply_visitor_filters(query, filters)
        
        # Get total count
        total = query.count()
        
        # Get paginated results
        visitors = query.order_by(desc(Visitor.last_visit)).offset(skip).limit(limit).all()
        
        pages = (total + limit - 1) // limit
        
        return VisitorListResponse(
            visitors=[VisitorResponse.from_orm(v) for v in visitors],
            total=total,
            page=(skip // limit) + 1,
            per_page=limit,
            pages=pages
        )
    
    async def get_visitor_detail(
        self,
        db: Session,
        *,
        visitor_id: UUID,
        user_id: UUID
    ) -> VisitorDetailResponse:
        """Get detailed visitor information"""
        visitor = await self.get_or_404(db, visitor_id)
        
        # Verify access through website ownership
        website = db.query(Website).filter(
            and_(Website.id == visitor.website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Get additional visitor details
        detail_data = VisitorResponse.from_orm(visitor).dict()
        
        # Add threat count
        threat_count = len(visitor.threats) if visitor.threats else 0
        detail_data["threat_count"] = threat_count
        
        # Add session duration
        if visitor.first_visit and visitor.last_visit:
            session_duration = int((visitor.last_visit - visitor.first_visit).total_seconds())
            detail_data["session_duration"] = session_duration
        
        # Calculate bounce rate (simplified)
        bounce_rate = 100.0 if visitor.total_pages == 1 else 0.0
        detail_data["bounce_rate"] = bounce_rate
        
        return VisitorDetailResponse(**detail_data)
    
    async def get_visitor_analytics(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID,
        days: int = 30
    ) -> VisitorAnalytics:
        """Get visitor analytics for a website"""
        # Verify website ownership
        website = db.query(Website).filter(
            and_(Website.id == website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Website not found or access denied"
            )
        
        # Date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Base query
        base_query = db.query(Visitor).filter(
            and_(
                Visitor.website_id == website_id,
                Visitor.created_at >= start_date
            )
        )
        
        # Basic metrics
        total_visitors = base_query.count()
        unique_visitors = base_query.distinct(Visitor.ip_address).count()
        returning_visitors = base_query.filter(Visitor.total_visits > 1).count()
        new_visitors = total_visitors - returning_visitors
        
        # Calculate averages
        avg_session_duration = db.query(func.avg(Visitor.total_time_spent)).filter(
            and_(
                Visitor.website_id == website_id,
                Visitor.created_at >= start_date
            )
        ).scalar() or 0.0
        
        avg_pages_per_session = db.query(func.avg(Visitor.total_pages)).filter(
            and_(
                Visitor.website_id == website_id,
                Visitor.created_at >= start_date
            )
        ).scalar() or 0.0
        
        # Bounce rate (visitors with only 1 page view)
        single_page_visitors = base_query.filter(Visitor.total_pages == 1).count()
        bounce_rate = (single_page_visitors / total_visitors * 100) if total_visitors > 0 else 0.0
        
        # Geographic data
        top_countries = self._get_top_countries(db, website_id, start_date)
        top_regions = self._get_top_regions(db, website_id, start_date)
        top_cities = self._get_top_cities(db, website_id, start_date)
        
        # Device data
        device_breakdown = self._get_device_breakdown(db, website_id, start_date)
        browser_breakdown = self._get_browser_breakdown(db, website_id, start_date)
        os_breakdown = self._get_os_breakdown(db, website_id, start_date)
        
        # Risk distribution
        risk_distribution = self._get_risk_distribution(db, website_id, start_date)
        
        # VPN/Proxy usage
        vpn_usage = base_query.filter(Visitor.is_vpn == True).count()
        proxy_usage = base_query.filter(Visitor.is_proxy == True).count()
        tor_usage = base_query.filter(Visitor.is_tor == True).count()
        
        # Time series data (simplified for now)
        hourly_visitors = []
        daily_visitors = []
        
        return VisitorAnalytics(
            total_visitors=total_visitors,
            unique_visitors=unique_visitors,
            returning_visitors=returning_visitors,
            new_visitors=new_visitors,
            bounce_rate=bounce_rate,
            avg_session_duration=avg_session_duration,
            page_views=int(db.query(func.sum(Visitor.total_pages)).filter(
                and_(Visitor.website_id == website_id, Visitor.created_at >= start_date)
            ).scalar() or 0),
            avg_pages_per_session=avg_pages_per_session,
            hourly_visitors=hourly_visitors,
            daily_visitors=daily_visitors,
            top_countries=top_countries,
            top_regions=top_regions,
            top_cities=top_cities,
            device_breakdown=device_breakdown,
            browser_breakdown=browser_breakdown,
            os_breakdown=os_breakdown,
            referrer_breakdown={},  # TODO: Implement when referrer tracking is added
            search_engines={},  # TODO: Implement when search engine tracking is added
            risk_distribution=risk_distribution,
            vpn_usage=vpn_usage,
            proxy_usage=proxy_usage,
            tor_usage=tor_usage
        )
    
    async def get_realtime_visitors(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID
    ) -> VisitorRealTimeData:
        """Get real-time visitor data"""
        # Verify website ownership
        website = db.query(Website).filter(
            and_(Website.id == website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Website not found or access denied"
            )
        
        # Active visitors (last 5 minutes)
        five_minutes_ago = datetime.utcnow() - timedelta(minutes=5)
        active_visitors = db.query(Visitor).filter(
            and_(
                Visitor.website_id == website_id,
                Visitor.last_visit >= five_minutes_ago
            )
        ).count()
        
        # Recent visitors (last 10)
        recent_visitors = db.query(Visitor).filter(
            Visitor.website_id == website_id
        ).order_by(desc(Visitor.last_visit)).limit(10).all()
        
        # Device breakdown for active visitors
        device_breakdown = self._get_device_breakdown(db, website_id, five_minutes_ago)
        
        return VisitorRealTimeData(
            active_visitors=active_visitors,
            recent_visitors=[VisitorResponse.from_orm(v) for v in recent_visitors],
            geographic_activity=[],  # TODO: Implement geographic activity
            device_breakdown=device_breakdown,
            threat_alerts=[],  # TODO: Implement threat alerts
            timestamp=datetime.utcnow()
        )
    
    async def block_visitor(
        self,
        db: Session,
        *,
        visitor_id: UUID,
        user_id: UUID,
        reason: Optional[str] = None
    ) -> Visitor:
        """Block a visitor"""
        visitor = await self.get_or_404(db, visitor_id)
        
        # Verify access through website ownership
        website = db.query(Website).filter(
            and_(Website.id == visitor.website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Update visitor to blocked status
        update_data = VisitorUpdate(is_blocked=True)
        return await self.update(db, db_obj=visitor, obj_in=update_data)
    
    async def bulk_action_visitors(
        self,
        db: Session,
        *,
        bulk_action: VisitorBulkAction,
        user_id: UUID
    ) -> Dict[str, Any]:
        """Perform bulk action on visitors"""
        # Verify all visitors belong to user's websites
        visitors = db.query(Visitor).join(Website).filter(
            and_(
                Visitor.id.in_(bulk_action.visitor_ids),
                Website.user_id == user_id
            )
        ).all()
        
        if len(visitors) != len(bulk_action.visitor_ids):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Some visitors not found or access denied"
            )
        
        # Perform action
        updated_count = 0
        if bulk_action.action == "block":
            for visitor in visitors:
                visitor.is_blocked = True
                updated_count += 1
        elif bulk_action.action == "unblock":
            for visitor in visitors:
                visitor.is_blocked = False
                updated_count += 1
        elif bulk_action.action == "update_risk":
            risk_level = bulk_action.parameters.get("risk_level") if bulk_action.parameters else None
            if risk_level:
                for visitor in visitors:
                    visitor.risk_level = risk_level
                    updated_count += 1
        
        db.commit()
        
        return {
            "action": bulk_action.action,
            "updated_count": updated_count,
            "total_requested": len(bulk_action.visitor_ids)
        }
    
    async def get_by_ip_and_website(
        self,
        db: Session,
        ip_address: str,
        website_id: UUID
    ) -> Optional[Visitor]:
        """Get visitor by IP address and website"""
        return db.query(Visitor).filter(
            and_(
                Visitor.ip_address == ip_address,
                Visitor.website_id == website_id
            )
        ).first()
    
    async def _enrich_visitor_data(self, visitor_data: VisitorCreate) -> VisitorCreate:
        """Enrich visitor data with geolocation and device information"""
        # TODO: Implement actual geolocation and device detection
        # For now, return the original data
        return visitor_data
    
    def _apply_visitor_filters(self, query, filters: VisitorFilter):
        """Apply filters to visitor query"""
        if filters.country:
            query = query.filter(Visitor.country == filters.country)
        if filters.region:
            query = query.filter(Visitor.region == filters.region)
        if filters.city:
            query = query.filter(Visitor.city == filters.city)
        if filters.device_type:
            query = query.filter(Visitor.device_type == filters.device_type)
        if filters.risk_level:
            query = query.filter(Visitor.risk_level == filters.risk_level)
        if filters.is_blocked is not None:
            query = query.filter(Visitor.is_blocked == filters.is_blocked)
        if filters.is_vpn is not None:
            query = query.filter(Visitor.is_vpn == filters.is_vpn)
        if filters.is_proxy is not None:
            query = query.filter(Visitor.is_proxy == filters.is_proxy)
        if filters.is_tor is not None:
            query = query.filter(Visitor.is_tor == filters.is_tor)
        if filters.date_from:
            query = query.filter(Visitor.created_at >= filters.date_from)
        if filters.date_to:
            query = query.filter(Visitor.created_at <= filters.date_to)
        if filters.min_visits:
            query = query.filter(Visitor.total_visits >= filters.min_visits)
        if filters.max_visits:
            query = query.filter(Visitor.total_visits <= filters.max_visits)
        
        return query
    
    def _get_top_countries(self, db: Session, website_id: UUID, start_date: datetime) -> List[Dict[str, Any]]:
        """Get top countries by visitor count"""
        results = db.query(
            Visitor.country,
            func.count(Visitor.id).label('count')
        ).filter(
            and_(
                Visitor.website_id == website_id,
                Visitor.created_at >= start_date,
                Visitor.country.isnot(None)
            )
        ).group_by(Visitor.country).order_by(desc('count')).limit(10).all()
        
        return [{"country": r.country, "count": r.count} for r in results]
    
    def _get_top_regions(self, db: Session, website_id: UUID, start_date: datetime) -> List[Dict[str, Any]]:
        """Get top regions by visitor count"""
        results = db.query(
            Visitor.region,
            func.count(Visitor.id).label('count')
        ).filter(
            and_(
                Visitor.website_id == website_id,
                Visitor.created_at >= start_date,
                Visitor.region.isnot(None)
            )
        ).group_by(Visitor.region).order_by(desc('count')).limit(10).all()
        
        return [{"region": r.region, "count": r.count} for r in results]
    
    def _get_top_cities(self, db: Session, website_id: UUID, start_date: datetime) -> List[Dict[str, Any]]:
        """Get top cities by visitor count"""
        results = db.query(
            Visitor.city,
            func.count(Visitor.id).label('count')
        ).filter(
            and_(
                Visitor.website_id == website_id,
                Visitor.created_at >= start_date,
                Visitor.city.isnot(None)
            )
        ).group_by(Visitor.city).order_by(desc('count')).limit(10).all()
        
        return [{"city": r.city, "count": r.count} for r in results]
    
    def _get_device_breakdown(self, db: Session, website_id: UUID, start_date: datetime) -> Dict[str, int]:
        """Get device type breakdown"""
        results = db.query(
            Visitor.device_type,
            func.count(Visitor.id).label('count')
        ).filter(
            and_(
                Visitor.website_id == website_id,
                Visitor.created_at >= start_date
            )
        ).group_by(Visitor.device_type).all()
        
        return {r.device_type or "unknown": r.count for r in results}
    
    def _get_browser_breakdown(self, db: Session, website_id: UUID, start_date: datetime) -> Dict[str, int]:
        """Get browser breakdown"""
        results = db.query(
            Visitor.browser,
            func.count(Visitor.id).label('count')
        ).filter(
            and_(
                Visitor.website_id == website_id,
                Visitor.created_at >= start_date,
                Visitor.browser.isnot(None)
            )
        ).group_by(Visitor.browser).all()
        
        return {r.browser: r.count for r in results}
    
    def _get_os_breakdown(self, db: Session, website_id: UUID, start_date: datetime) -> Dict[str, int]:
        """Get operating system breakdown"""
        results = db.query(
            Visitor.os,
            func.count(Visitor.id).label('count')
        ).filter(
            and_(
                Visitor.website_id == website_id,
                Visitor.created_at >= start_date,
                Visitor.os.isnot(None)
            )
        ).group_by(Visitor.os).all()
        
        return {r.os: r.count for r in results}
    
    def _get_risk_distribution(self, db: Session, website_id: UUID, start_date: datetime) -> Dict[str, int]:
        """Get risk level distribution"""
        results = db.query(
            Visitor.risk_level,
            func.count(Visitor.id).label('count')
        ).filter(
            and_(
                Visitor.website_id == website_id,
                Visitor.created_at >= start_date
            )
        ).group_by(Visitor.risk_level).all()
        
        return {r.risk_level: r.count for r in results}
