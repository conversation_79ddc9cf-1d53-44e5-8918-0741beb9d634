"""
Blocking system API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional, List
from uuid import UUID

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.blocking import BlockingRuleType, BlockingAction
from app.schemas.blocking import (
    BlockingRuleCreate, BlockingRuleUpdate, BlockingRuleResponse,
    BlockingRuleListResponse, WhitelistEntryCreate, WhitelistEntryUpdate,
    WhitelistEntryResponse, WhitelistListResponse, IPBlockRequest,
    CountryBlockRequest, BulkBlockRequest, BlockingAnalytics,
    BlockingEventResponse, BlockingEventListResponse
)
from app.services.blocking_service import blocking_service
from app.services.whitelist_service import whitelist_service

router = APIRouter()


# Blocking Rules Endpoints
@router.post("/rules", response_model=BlockingRuleResponse)
async def create_blocking_rule(
    rule_data: BlockingRuleCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new blocking rule"""
    try:
        rule = await blocking_service.create_blocking_rule(
            db, rule_data=rule_data, user_id=current_user.id
        )
        return rule
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/rules", response_model=BlockingRuleListResponse)
async def get_blocking_rules(
    website_id: Optional[UUID] = Query(None, description="Filter by website ID"),
    rule_type: Optional[BlockingRuleType] = Query(None, description="Filter by rule type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=500, description="Number of records to return"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get blocking rules for the current user"""
    result = await blocking_service.get_user_blocking_rules(
        db,
        user_id=current_user.id,
        website_id=website_id,
        rule_type=rule_type,
        is_active=is_active,
        skip=skip,
        limit=limit
    )
    
    return BlockingRuleListResponse(
        rules=[BlockingRuleResponse.from_attributes(rule) for rule in result["rules"]],
        total=result["total"],
        page=result["page"],
        per_page=result["per_page"],
        has_next=result["has_next"],
        has_prev=result["has_prev"]
    )


@router.get("/rules/{rule_id}", response_model=BlockingRuleResponse)
async def get_blocking_rule(
    rule_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific blocking rule"""
    rule = await blocking_service.get(db, id=rule_id)
    if not rule or rule.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Blocking rule not found")
    
    return rule


@router.put("/rules/{rule_id}", response_model=BlockingRuleResponse)
async def update_blocking_rule(
    rule_id: UUID,
    rule_data: BlockingRuleUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a blocking rule"""
    try:
        rule = await blocking_service.update_blocking_rule(
            db, rule_id=rule_id, rule_data=rule_data, user_id=current_user.id
        )
        return rule
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.delete("/rules/{rule_id}")
async def delete_blocking_rule(
    rule_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a blocking rule"""
    try:
        success = await blocking_service.delete_blocking_rule(
            db, rule_id=rule_id, user_id=current_user.id
        )
        if success:
            return {"message": "Blocking rule deleted successfully"}
        else:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Blocking rule not found")
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


# Quick Blocking Actions
@router.post("/ip", response_model=BlockingRuleResponse)
async def block_ip_address(
    request_data: IPBlockRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Block a specific IP address"""
    try:
        rule = await blocking_service.block_ip_address(
            db, request_data=request_data, user_id=current_user.id
        )
        return rule
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/country", response_model=BlockingRuleResponse)
async def block_country(
    request_data: CountryBlockRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Block a specific country"""
    try:
        rule = await blocking_service.block_country(
            db, request_data=request_data, user_id=current_user.id
        )
        return rule
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/bulk", response_model=List[BlockingRuleResponse])
async def bulk_block(
    request_data: BulkBlockRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create multiple blocking rules"""
    try:
        rules = await blocking_service.bulk_block(
            db, request_data=request_data, user_id=current_user.id
        )
        return rules
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# Whitelist Endpoints
@router.get("/whitelist", response_model=WhitelistListResponse)
async def get_whitelist_entries(
    website_id: Optional[UUID] = Query(None, description="Filter by website ID"),
    entry_type: Optional[str] = Query(None, description="Filter by entry type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=500, description="Number of records to return"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get whitelist entries for the current user"""
    result = await whitelist_service.get_user_whitelist_entries(
        db,
        user_id=current_user.id,
        website_id=website_id,
        entry_type=entry_type,
        is_active=is_active,
        skip=skip,
        limit=limit
    )
    
    return WhitelistListResponse(
        entries=[WhitelistEntryResponse.from_attributes(entry) for entry in result["entries"]],
        total=result["total"],
        page=result["page"],
        per_page=result["per_page"],
        has_next=result["has_next"],
        has_prev=result["has_prev"]
    )


@router.post("/whitelist", response_model=WhitelistEntryResponse)
async def create_whitelist_entry(
    entry_data: WhitelistEntryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Add entry to whitelist"""
    try:
        entry = await whitelist_service.create_whitelist_entry(
            db, entry_data=entry_data, user_id=current_user.id
        )
        return entry
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.put("/whitelist/{entry_id}", response_model=WhitelistEntryResponse)
async def update_whitelist_entry(
    entry_id: UUID,
    entry_data: WhitelistEntryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update whitelist entry"""
    try:
        entry = await whitelist_service.update_whitelist_entry(
            db, entry_id=entry_id, entry_data=entry_data, user_id=current_user.id
        )
        return entry
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.delete("/whitelist/{entry_id}")
async def delete_whitelist_entry(
    entry_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete whitelist entry"""
    try:
        success = await whitelist_service.delete_whitelist_entry(
            db, entry_id=entry_id, user_id=current_user.id
        )
        if success:
            return {"message": "Whitelist entry deleted successfully"}
        else:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Whitelist entry not found")
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


# Analytics and Monitoring
@router.get("/analytics", response_model=BlockingAnalytics)
async def get_blocking_analytics(
    website_id: Optional[UUID] = Query(None, description="Filter by website ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get blocking system analytics"""
    # This would implement comprehensive analytics
    # For now, return basic analytics
    return BlockingAnalytics(
        total_rules=0,
        active_rules=0,
        temporary_rules=0,
        total_blocks_today=0,
        total_blocks_week=0,
        total_blocks_month=0,
        top_blocked_countries=[],
        top_blocked_ips=[],
        blocking_trends=[],
        rule_effectiveness=[]
    )


@router.get("/events", response_model=BlockingEventListResponse)
async def get_blocking_events(
    website_id: Optional[UUID] = Query(None, description="Filter by website ID"),
    event_type: Optional[str] = Query(None, description="Filter by event type"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=500, description="Number of records to return"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get blocking events log"""
    # This would implement event log retrieval
    # For now, return empty list
    return BlockingEventListResponse(
        events=[],
        total=0,
        page=1,
        per_page=limit,
        has_next=False,
        has_prev=False
    )
