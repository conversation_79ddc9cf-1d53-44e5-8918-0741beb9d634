"""
Main ML threat detector interface
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from uuid import UUID
from pathlib import Path

from sqlalchemy.orm import Session

from app.ml.models import EnsembleDetector, AnomalyDetector, BehaviorAnalyzer
from app.ml.features import FeatureExtractor, RequestFeatures
from app.models.threat import Threat, ThreatType, ThreatSeverity
from app.models.visitor import Visitor
from app.schemas.threat import ThreatCreate

logger = logging.getLogger(__name__)


class ThreatDetector:
    """Base threat detector interface"""
    
    def __init__(self):
        self.feature_extractor = FeatureExtractor()
    
    async def detect_threats(
        self,
        request_data: Dict[str, Any],
        website_id: UUID,
        behavioral_data: Optional[Dict[str, Any]] = None
    ) -> List[ThreatCreate]:
        """Detect threats in request data"""
        raise NotImplementedError
    
    async def analyze_request(
        self,
        request_data: Dict[str, Any],
        behavioral_data: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, float, str]:
        """
        Analyze single request
        Returns: (is_threat, confidence, threat_type)
        """
        raise NotImplementedError


class MLThreatDetector(ThreatDetector):
    """Machine Learning-based threat detector"""
    
    def __init__(self, model_path: Optional[str] = None):
        super().__init__()
        self.model = EnsembleDetector()
        self.model_path = model_path or "models/threat_detector.pkl"
        self.is_initialized = False
        
        # Threat type mapping based on features
        self.threat_type_rules = {
            'sql_injection': lambda f: f.has_sql_keywords and f.param_entropy > 3.0,
            'xss': lambda f: f.has_script_tags,
            'brute_force': lambda f: f.request_frequency > 10 and f.error_rate > 0.3,
            'ddos': lambda f: f.request_frequency > 20,
            'suspicious_activity': lambda f: f.is_tor or f.is_vpn or f.country_risk_score > 0.5,
            'unauthorized_access': lambda f: f.missing_common_headers > 4 or f.is_bot_user_agent,
            'rate_limit_exceeded': lambda f: f.request_frequency > 15,
            'invalid_input': lambda f: f.param_entropy > 5.0 or f.url_suspicious_chars > 10
        }
        
        # Initialize model
        asyncio.create_task(self._initialize_model())
    
    async def _initialize_model(self):
        """Initialize ML model"""
        try:
            # Try to load existing model
            if Path(self.model_path).exists():
                success = self.model.load_model(self.model_path)
                if success:
                    logger.info("ML threat detection model loaded successfully")
                    self.is_initialized = True
                    return
            
            # If no model exists, use rule-based detection
            logger.warning("No trained ML model found, using rule-based detection")
            self.is_initialized = True
            
        except Exception as e:
            logger.error(f"Error initializing ML model: {e}")
            self.is_initialized = True  # Fall back to rule-based
    
    async def detect_threats(
        self,
        request_data: Dict[str, Any],
        website_id: UUID,
        behavioral_data: Optional[Dict[str, Any]] = None
    ) -> List[ThreatCreate]:
        """Detect threats using ML models"""
        if not self.is_initialized:
            await self._initialize_model()
        
        threats = []
        
        try:
            # Extract features
            features = self.feature_extractor.extract_features(
                request_data, behavioral_data
            )
            
            # Analyze with ML model
            is_threat, confidence, threat_type = await self.analyze_request(
                request_data, behavioral_data
            )
            
            if is_threat and confidence > 50:  # Threshold for threat creation
                # Determine severity based on confidence and threat type
                severity = self._determine_severity(confidence, threat_type, features)
                
                # Create threat record
                threat_data = ThreatCreate(
                    website_id=website_id,
                    threat_type=ThreatType(threat_type),
                    severity=severity,
                    confidence_score=int(confidence),
                    title=f"ML-detected {threat_type.replace('_', ' ').title()}",
                    description=self._generate_threat_description(threat_type, features),
                    attack_vector=request_data.get('url', ''),
                    source_ip=request_data.get('ip_address'),
                    source_country=request_data.get('country'),
                    user_agent=request_data.get('user_agent'),
                    request_method=request_data.get('method'),
                    request_url=request_data.get('url'),
                    request_headers=request_data.get('headers'),
                    request_body=request_data.get('body'),
                    detection_method="ml_based",
                    rule_id=f"ml_{threat_type}",
                    threat_metadata={
                        'ml_confidence': confidence,
                        'feature_scores': self._get_feature_importance(features),
                        'model_version': self.model.model_version
                    }
                )
                
                threats.append(threat_data)
        
        except Exception as e:
            logger.error(f"Error in ML threat detection: {e}")
            # Fall back to simple rule-based detection
            threats.extend(await self._fallback_detection(request_data, website_id))
        
        return threats
    
    async def analyze_request(
        self,
        request_data: Dict[str, Any],
        behavioral_data: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, float, str]:
        """Analyze single request with ML model"""
        try:
            # Extract features
            features = self.feature_extractor.extract_features(
                request_data, behavioral_data
            )
            
            # Get ML prediction
            is_threat, confidence = self.model.predict(features)
            
            # Determine threat type
            threat_type = self._classify_threat_type(features)
            
            return is_threat, confidence, threat_type
            
        except Exception as e:
            logger.error(f"Error in request analysis: {e}")
            return False, 0.0, "unknown"
    
    def _classify_threat_type(self, features: RequestFeatures) -> str:
        """Classify threat type based on features"""
        # Check each threat type rule
        for threat_type, rule_func in self.threat_type_rules.items():
            if rule_func(features):
                return threat_type
        
        # Default to suspicious activity
        return "suspicious_activity"
    
    def _determine_severity(
        self,
        confidence: float,
        threat_type: str,
        features: RequestFeatures
    ) -> ThreatSeverity:
        """Determine threat severity"""
        # Base severity on threat type
        type_severity = {
            'sql_injection': ThreatSeverity.HIGH,
            'xss': ThreatSeverity.MEDIUM,
            'brute_force': ThreatSeverity.MEDIUM,
            'ddos': ThreatSeverity.HIGH,
            'suspicious_activity': ThreatSeverity.LOW,
            'unauthorized_access': ThreatSeverity.MEDIUM,
            'rate_limit_exceeded': ThreatSeverity.LOW,
            'invalid_input': ThreatSeverity.LOW
        }
        
        base_severity = type_severity.get(threat_type, ThreatSeverity.LOW)
        
        # Adjust based on confidence
        if confidence > 90:
            if base_severity == ThreatSeverity.HIGH:
                return ThreatSeverity.CRITICAL
            elif base_severity == ThreatSeverity.MEDIUM:
                return ThreatSeverity.HIGH
        elif confidence < 60:
            if base_severity == ThreatSeverity.HIGH:
                return ThreatSeverity.MEDIUM
            elif base_severity == ThreatSeverity.MEDIUM:
                return ThreatSeverity.LOW
        
        return base_severity
    
    def _generate_threat_description(
        self,
        threat_type: str,
        features: RequestFeatures
    ) -> str:
        """Generate human-readable threat description"""
        descriptions = {
            'sql_injection': f"Potential SQL injection detected with {features.param_count} parameters and SQL keywords present",
            'xss': f"Cross-site scripting attempt detected with script tags in request",
            'brute_force': f"Brute force attack detected with {features.request_frequency:.1f} requests/minute",
            'ddos': f"DDoS attack pattern detected with extremely high request frequency",
            'suspicious_activity': f"Suspicious activity from {'Tor' if features.is_tor else 'VPN' if features.is_vpn else 'high-risk location'}",
            'unauthorized_access': f"Unauthorized access attempt detected with bot-like behavior",
            'rate_limit_exceeded': f"Rate limit exceeded with {features.request_frequency:.1f} requests/minute",
            'invalid_input': f"Invalid input detected with high entropy ({features.param_entropy:.2f}) and suspicious characters"
        }
        
        return descriptions.get(threat_type, f"Machine learning detected {threat_type} threat")
    
    def _get_feature_importance(self, features: RequestFeatures) -> Dict[str, float]:
        """Get important features that contributed to threat detection"""
        importance = {}
        
        if features.has_sql_keywords:
            importance['sql_keywords'] = 0.9
        if features.has_script_tags:
            importance['script_tags'] = 0.8
        if features.url_suspicious_chars > 5:
            importance['suspicious_chars'] = min(1.0, features.url_suspicious_chars / 20)
        if features.param_entropy > 3.0:
            importance['param_entropy'] = min(1.0, features.param_entropy / 6)
        if features.request_frequency > 5:
            importance['request_frequency'] = min(1.0, features.request_frequency / 20)
        if features.is_tor or features.is_vpn:
            importance['anonymization'] = 0.7
        if features.country_risk_score > 0.5:
            importance['geographic_risk'] = features.country_risk_score
        
        return importance
    
    async def _fallback_detection(
        self,
        request_data: Dict[str, Any],
        website_id: UUID
    ) -> List[ThreatCreate]:
        """Fallback rule-based detection"""
        threats = []
        
        # Simple SQL injection detection
        url = request_data.get('url', '')
        body = request_data.get('body', '')
        combined_text = f"{url} {body}".lower()
        
        sql_keywords = ['select', 'insert', 'update', 'delete', 'union', 'drop']
        if any(keyword in combined_text for keyword in sql_keywords):
            threats.append(ThreatCreate(
                website_id=website_id,
                threat_type=ThreatType.SQL_INJECTION,
                severity=ThreatSeverity.HIGH,
                confidence_score=75,
                title="SQL Injection Attempt (Rule-based)",
                description="Potential SQL injection detected using rule-based analysis",
                attack_vector=url,
                source_ip=request_data.get('ip_address'),
                detection_method="rule_based",
                rule_id="fallback_sql"
            ))
        
        # Simple XSS detection
        script_patterns = ['<script', 'javascript:', 'onerror=', 'onload=']
        if any(pattern in combined_text for pattern in script_patterns):
            threats.append(ThreatCreate(
                website_id=website_id,
                threat_type=ThreatType.XSS,
                severity=ThreatSeverity.MEDIUM,
                confidence_score=70,
                title="XSS Attempt (Rule-based)",
                description="Potential cross-site scripting detected using rule-based analysis",
                attack_vector=url,
                source_ip=request_data.get('ip_address'),
                detection_method="rule_based",
                rule_id="fallback_xss"
            ))
        
        return threats
    
    async def train_model(
        self,
        db: Session,
        website_id: Optional[UUID] = None,
        days_back: int = 30
    ):
        """Train ML model with historical data"""
        try:
            # Collect training data from database
            training_data = await self._collect_training_data(db, website_id, days_back)
            
            if len(training_data) < 100:
                logger.warning("Insufficient training data for ML model")
                return False
            
            # Train model
            success = self.model.train(training_data)
            
            if success:
                # Save trained model
                self.model.save_model(self.model_path)
                logger.info(f"ML model trained and saved with {len(training_data)} samples")
            
            return success
            
        except Exception as e:
            logger.error(f"Error training ML model: {e}")
            return False
    
    async def _collect_training_data(
        self,
        db: Session,
        website_id: Optional[UUID],
        days_back: int
    ) -> List[Tuple[RequestFeatures, bool]]:
        """Collect training data from historical threats and normal traffic"""
        training_data = []
        
        # This would collect actual data from the database
        # For now, return empty list as placeholder
        # In real implementation, would query threats and visitors tables
        
        return training_data


# Global ML threat detector instance
ml_threat_detector = MLThreatDetector()
