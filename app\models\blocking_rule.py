"""
Blocking rule model for intelligent blocking system
"""

from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON>ole<PERSON>, DateTime, Integer, ForeignKey, Text, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum

from app.core.database import Base


class RuleType(str, enum.Enum):
    IP_ADDRESS = "ip"
    IP_RANGE = "ip_range"
    COUNTRY = "country"
    REGION = "region"
    ISP = "isp"
    USER_AGENT = "user_agent"
    DEVICE_TYPE = "device_type"
    THREAT_SCORE = "threat_score"
    CUSTOM = "custom"


class BlockingAction(str, enum.Enum):
    BLOCK = "block"
    ALLOW = "allow"
    MONITOR = "monitor"
    CHALLENGE = "challenge"
    RATE_LIMIT = "rate_limit"


class BlockingRule(Base):
    __tablename__ = "blocking_rules"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    website_id = Column(UUID(as_uuid=True), ForeignKey("websites.id"), nullable=False)
    
    # Rule configuration
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    rule_type = Column(Enum(RuleType), nullable=False)
    rule_value = Column(String(255), nullable=False)
    action = Column(Enum(BlockingAction), default=BlockingAction.BLOCK, nullable=False)
    
    # Rule conditions
    conditions = Column(JSONB, nullable=True)  # Additional conditions as JSON
    priority = Column(Integer, default=100, nullable=False)  # Lower number = higher priority
    
    # Status and timing
    is_active = Column(Boolean, default=True, nullable=False)
    is_temporary = Column(Boolean, default=False, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Rate limiting (if action is rate_limit)
    rate_limit_requests = Column(Integer, nullable=True)
    rate_limit_window = Column(Integer, nullable=True)  # seconds
    
    # Statistics
    matches_count = Column(Integer, default=0, nullable=False)
    blocks_count = Column(Integer, default=0, nullable=False)
    last_matched_at = Column(DateTime(timezone=True), nullable=True)
    
    # Metadata
    created_by = Column(String(100), nullable=True)  # user, system, auto
    rule_metadata = Column(JSONB, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    website = relationship("Website", back_populates="blocking_rules")
    
    def __repr__(self):
        return f"<BlockingRule(type='{self.rule_type}', action='{self.action}')>"
