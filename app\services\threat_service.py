"""
Threat service for business logic
"""

import re
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, List, Dict, Any, Pattern
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException, status

from app.models.threat import Threat, ThreatSeverity, ThreatType
from app.models.website import Website
from app.models.visitor import Visitor
from app.schemas.threat import (
    ThreatCreate, ThreatUpdate, ThreatResponse, ThreatDetailResponse,
    ThreatListResponse, ThreatAnalytics, ThreatRealTimeData, ThreatFilter,
    ThreatBulkAction, ThreatRule, ThreatRuleResponse
)
from app.services.base_service import BaseService


class ThreatService(BaseService[Threat, ThreatCreate, ThreatUpdate]):
    """Threat service with business logic"""
    
    def __init__(self):
        super().__init__(Threat)
        self.threat_rules = self._load_default_threat_rules()
    
    async def create_threat(
        self,
        db: Session,
        *,
        threat_data: ThreatCreate
    ) -> Threat:
        """Create a new threat record"""
        # Validate website exists
        website = db.query(Website).filter(Website.id == threat_data.website_id).first()
        if not website:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Website not found"
            )
        
        # Create threat
        threat = await self.create(db, obj_in=threat_data)
        
        # Auto-block if severity is critical or high
        if threat.severity in [ThreatSeverity.CRITICAL, ThreatSeverity.HIGH]:
            await self._auto_block_threat(db, threat)
        
        return threat
    
    async def detect_threats(
        self,
        db: Session,
        *,
        website_id: UUID,
        request_data: Dict[str, Any]
    ) -> List[Threat]:
        """Detect threats in request data"""
        detected_threats = []
        
        # Apply threat detection rules
        for rule in self.threat_rules:
            if await self._check_threat_rule(request_data, rule):
                threat_data = ThreatCreate(
                    website_id=website_id,
                    threat_type=rule["threat_type"],
                    severity=rule["severity"],
                    confidence_score=rule["confidence_score"],
                    title=rule["title"],
                    description=rule["description"],
                    detection_method="rule_based",
                    rule_id=rule["id"],
                    request_url=request_data.get("url"),
                    request_method=request_data.get("method"),
                    request_headers=request_data.get("headers"),
                    request_body=request_data.get("body"),
                    source_ip=request_data.get("ip"),
                    user_agent=request_data.get("user_agent")
                )
                
                threat = await self.create_threat(db, threat_data=threat_data)
                detected_threats.append(threat)
        
        return detected_threats
    
    async def get_website_threats(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[ThreatFilter] = None
    ) -> ThreatListResponse:
        """Get threats for a specific website"""
        # Verify website ownership
        website = db.query(Website).filter(
            and_(Website.id == website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Website not found or access denied"
            )
        
        # Build query with filters
        query = db.query(Threat).filter(Threat.website_id == website_id)
        
        if filters:
            query = self._apply_threat_filters(query, filters)
        
        # Get total count
        total = query.count()
        
        # Get paginated results
        threats = query.order_by(desc(Threat.detected_at)).offset(skip).limit(limit).all()
        
        pages = (total + limit - 1) // limit
        
        return ThreatListResponse(
            threats=[ThreatResponse.from_orm(t) for t in threats],
            total=total,
            page=(skip // limit) + 1,
            per_page=limit,
            pages=pages
        )
    
    async def get_threat_detail(
        self,
        db: Session,
        *,
        threat_id: UUID,
        user_id: UUID
    ) -> ThreatDetailResponse:
        """Get detailed threat information"""
        threat = await self.get_or_404(db, threat_id)
        
        # Verify access through website ownership
        website = db.query(Website).filter(
            and_(Website.id == threat.website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Get additional threat details
        detail_data = ThreatResponse.from_orm(threat).dict()
        
        # Add visitor info if available
        if threat.visitor_id:
            visitor = db.query(Visitor).filter(Visitor.id == threat.visitor_id).first()
            if visitor:
                detail_data["visitor_info"] = {
                    "ip_address": str(visitor.ip_address),
                    "country": visitor.country,
                    "device_type": visitor.device_type,
                    "browser": visitor.browser
                }
        
        # Find related threats (same IP, same type, within 24 hours)
        related_threats = db.query(Threat).filter(
            and_(
                Threat.website_id == threat.website_id,
                Threat.source_ip == threat.source_ip,
                Threat.threat_type == threat.threat_type,
                Threat.detected_at >= threat.detected_at - timedelta(hours=24),
                Threat.id != threat.id
            )
        ).limit(5).all()
        
        detail_data["related_threats"] = [t.id for t in related_threats]
        
        # Add remediation steps
        detail_data["remediation_steps"] = self._get_remediation_steps(threat.threat_type)
        
        return ThreatDetailResponse(**detail_data)
    
    async def get_threat_analytics(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID,
        days: int = 30
    ) -> ThreatAnalytics:
        """Get threat analytics for a website"""
        # Verify website ownership
        website = db.query(Website).filter(
            and_(Website.id == website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Website not found or access denied"
            )
        
        # Date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Base query
        base_query = db.query(Threat).filter(
            and_(
                Threat.website_id == website_id,
                Threat.detected_at >= start_date
            )
        )
        
        # Basic metrics
        total_threats = base_query.count()
        blocked_threats = base_query.filter(Threat.is_blocked == True).count()
        false_positives = base_query.filter(Threat.is_false_positive == True).count()
        
        # Average confidence score
        avg_confidence = db.query(func.avg(Threat.confidence_score)).filter(
            and_(
                Threat.website_id == website_id,
                Threat.detected_at >= start_date
            )
        ).scalar() or 0.0
        
        # Severity breakdown
        severity_counts = db.query(
            Threat.severity,
            func.count(Threat.id).label('count')
        ).filter(
            and_(
                Threat.website_id == website_id,
                Threat.detected_at >= start_date
            )
        ).group_by(Threat.severity).all()
        
        threats_by_severity = {s.severity: s.count for s in severity_counts}
        
        # Type breakdown
        type_counts = db.query(
            Threat.threat_type,
            func.count(Threat.id).label('count')
        ).filter(
            and_(
                Threat.website_id == website_id,
                Threat.detected_at >= start_date
            )
        ).group_by(Threat.threat_type).all()
        
        threats_by_type = {t.threat_type: t.count for t in type_counts}
        
        # Country breakdown
        country_counts = db.query(
            Threat.source_country,
            func.count(Threat.id).label('count')
        ).filter(
            and_(
                Threat.website_id == website_id,
                Threat.detected_at >= start_date,
                Threat.source_country.isnot(None)
            )
        ).group_by(Threat.source_country).order_by(desc('count')).limit(10).all()
        
        threats_by_country = [{"country": c.source_country, "count": c.count} for c in country_counts]
        
        # Calculate metrics
        critical_threats = threats_by_severity.get(ThreatSeverity.CRITICAL, 0)
        high_threats = threats_by_severity.get(ThreatSeverity.HIGH, 0)
        medium_threats = threats_by_severity.get(ThreatSeverity.MEDIUM, 0)
        low_threats = threats_by_severity.get(ThreatSeverity.LOW, 0)
        
        return ThreatAnalytics(
            total_threats=total_threats,
            threats_by_severity=threats_by_severity,
            threats_by_type=threats_by_type,
            threats_by_country=threats_by_country,
            blocked_threats=blocked_threats,
            false_positives=false_positives,
            avg_confidence_score=avg_confidence,
            threat_trend=[],  # TODO: Implement trend analysis
            top_attack_vectors=[],  # TODO: Implement attack vector analysis
            critical_threats=critical_threats,
            high_threats=high_threats,
            medium_threats=medium_threats,
            low_threats=low_threats
        )
    
    async def get_realtime_threats(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID
    ) -> ThreatRealTimeData:
        """Get real-time threat data"""
        # Verify website ownership
        website = db.query(Website).filter(
            and_(Website.id == website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Website not found or access denied"
            )
        
        # Active threats (last 5 minutes)
        five_minutes_ago = datetime.utcnow() - timedelta(minutes=5)
        active_threats = db.query(Threat).filter(
            and_(
                Threat.website_id == website_id,
                Threat.detected_at >= five_minutes_ago,
                Threat.is_false_positive == False
            )
        ).count()
        
        # Recent threats (last 10)
        recent_threats = db.query(Threat).filter(
            and_(
                Threat.website_id == website_id,
                Threat.is_false_positive == False
            )
        ).order_by(desc(Threat.detected_at)).limit(10).all()
        
        # Severity breakdown for recent threats
        severity_breakdown = {}
        for threat in recent_threats:
            severity_breakdown[threat.severity] = severity_breakdown.get(threat.severity, 0) + 1
        
        # Blocked IPs (last hour)
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        blocked_ips = db.query(Threat.source_ip).filter(
            and_(
                Threat.website_id == website_id,
                Threat.detected_at >= one_hour_ago,
                Threat.is_blocked == True,
                Threat.source_ip.isnot(None)
            )
        ).distinct().all()
        
        return ThreatRealTimeData(
            active_threats=active_threats,
            recent_threats=[ThreatResponse.from_orm(t) for t in recent_threats],
            threat_map=[],  # TODO: Implement threat mapping
            severity_breakdown=severity_breakdown,
            blocked_ips=[ip[0] for ip in blocked_ips],
            timestamp=datetime.utcnow()
        )
    
    async def resolve_threat(
        self,
        db: Session,
        *,
        threat_id: UUID,
        user_id: UUID,
        resolution_notes: Optional[str] = None
    ) -> Threat:
        """Resolve a threat"""
        threat = await self.get_or_404(db, threat_id)
        
        # Verify access through website ownership
        website = db.query(Website).filter(
            and_(Website.id == threat.website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Update threat
        update_data = ThreatUpdate(resolved_at=datetime.utcnow())
        return await self.update(db, db_obj=threat, obj_in=update_data)
    
    async def mark_false_positive(
        self,
        db: Session,
        *,
        threat_id: UUID,
        user_id: UUID
    ) -> Threat:
        """Mark threat as false positive"""
        threat = await self.get_or_404(db, threat_id)
        
        # Verify access through website ownership
        website = db.query(Website).filter(
            and_(Website.id == threat.website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Update threat
        update_data = ThreatUpdate(is_false_positive=True)
        return await self.update(db, db_obj=threat, obj_in=update_data)
    
    async def bulk_action_threats(
        self,
        db: Session,
        *,
        bulk_action: ThreatBulkAction,
        user_id: UUID
    ) -> Dict[str, Any]:
        """Perform bulk action on threats"""
        # Verify all threats belong to user's websites
        threats = db.query(Threat).join(Website).filter(
            and_(
                Threat.id.in_(bulk_action.threat_ids),
                Website.user_id == user_id
            )
        ).all()
        
        if len(threats) != len(bulk_action.threat_ids):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Some threats not found or access denied"
            )
        
        # Perform action
        updated_count = 0
        if bulk_action.action == "resolve":
            for threat in threats:
                threat.resolved_at = datetime.utcnow()
                updated_count += 1
        elif bulk_action.action == "mark_false_positive":
            for threat in threats:
                threat.is_false_positive = True
                updated_count += 1
        elif bulk_action.action == "block":
            for threat in threats:
                threat.is_blocked = True
                updated_count += 1
        
        db.commit()
        
        return {
            "action": bulk_action.action,
            "updated_count": updated_count,
            "total_requested": len(bulk_action.threat_ids)
        }
    
    def _load_default_threat_rules(self) -> List[Dict[str, Any]]:
        """Load default threat detection rules"""
        return [
            {
                "id": "sql_injection_1",
                "threat_type": ThreatType.SQL_INJECTION,
                "severity": ThreatSeverity.HIGH,
                "confidence_score": 85,
                "title": "SQL Injection Attempt",
                "description": "Detected potential SQL injection in request",
                "pattern": r"(\b(union|select|insert|update|delete|drop|create|alter)\b.*\b(from|where|into|values)\b)|('.*'.*=.*')|(\b(or|and)\b.*\b(1=1|1=0)\b)",
                "fields": ["url", "body", "query_params"]
            },
            {
                "id": "xss_1",
                "threat_type": ThreatType.XSS,
                "severity": ThreatSeverity.MEDIUM,
                "confidence_score": 80,
                "title": "Cross-Site Scripting Attempt",
                "description": "Detected potential XSS attack",
                "pattern": r"<script[^>]*>.*</script>|javascript:|on\w+\s*=|<iframe|<object|<embed",
                "fields": ["url", "body", "query_params"]
            },
            {
                "id": "brute_force_1",
                "threat_type": ThreatType.BRUTE_FORCE,
                "severity": ThreatSeverity.MEDIUM,
                "confidence_score": 90,
                "title": "Brute Force Attack",
                "description": "Multiple failed login attempts detected",
                "pattern": None,  # This would be detected by rate limiting
                "fields": []
            }
        ]
    
    async def _check_threat_rule(self, request_data: Dict[str, Any], rule: Dict[str, Any]) -> bool:
        """Check if request data matches a threat rule"""
        if not rule.get("pattern"):
            return False
        
        pattern = re.compile(rule["pattern"], re.IGNORECASE)
        
        # Check specified fields
        for field in rule.get("fields", []):
            value = request_data.get(field, "")
            if isinstance(value, str) and pattern.search(value):
                return True
            elif isinstance(value, dict):
                # Check dictionary values (like query params)
                for v in value.values():
                    if isinstance(v, str) and pattern.search(v):
                        return True
        
        return False
    
    async def _auto_block_threat(self, db: Session, threat: Threat) -> None:
        """Automatically block high-severity threats"""
        if threat.source_ip:
            # Update threat to blocked status
            threat.is_blocked = True
            threat.mitigation_action = "auto_blocked"
            db.commit()
            
            # TODO: Add IP to blocking rules
    
    def _apply_threat_filters(self, query, filters: ThreatFilter):
        """Apply filters to threat query"""
        if filters.threat_type:
            query = query.filter(Threat.threat_type == filters.threat_type)
        if filters.severity:
            query = query.filter(Threat.severity == filters.severity)
        if filters.detection_method:
            query = query.filter(Threat.detection_method == filters.detection_method)
        if filters.source_country:
            query = query.filter(Threat.source_country == filters.source_country)
        if filters.is_blocked is not None:
            query = query.filter(Threat.is_blocked == filters.is_blocked)
        if filters.is_false_positive is not None:
            query = query.filter(Threat.is_false_positive == filters.is_false_positive)
        if filters.date_from:
            query = query.filter(Threat.detected_at >= filters.date_from)
        if filters.date_to:
            query = query.filter(Threat.detected_at <= filters.date_to)
        if filters.min_confidence:
            query = query.filter(Threat.confidence_score >= filters.min_confidence)
        if filters.max_confidence:
            query = query.filter(Threat.confidence_score <= filters.max_confidence)
        
        return query
    
    def _get_remediation_steps(self, threat_type: str) -> List[str]:
        """Get remediation steps for a threat type"""
        remediation_map = {
            ThreatType.SQL_INJECTION: [
                "Use parameterized queries or prepared statements",
                "Validate and sanitize all user inputs",
                "Implement proper error handling",
                "Use least privilege database access",
                "Regular security code reviews"
            ],
            ThreatType.XSS: [
                "Encode output data properly",
                "Validate and sanitize user inputs",
                "Use Content Security Policy (CSP)",
                "Implement proper session management",
                "Regular security testing"
            ],
            ThreatType.BRUTE_FORCE: [
                "Implement account lockout policies",
                "Use strong password requirements",
                "Enable multi-factor authentication",
                "Monitor and alert on failed attempts",
                "Consider CAPTCHA for repeated failures"
            ]
        }
        
        return remediation_map.get(threat_type, ["Review and investigate the threat", "Implement appropriate security measures"])
