"""
WebSocket endpoints for real-time features
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional, List
from uuid import UUID

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.websocket.handlers import websocket_handler
from app.websocket.manager import websocket_manager
from app.websocket.events import (
    EventType, VisitorEvent, ThreatEvent, ScanEvent, SystemEvent, AnalyticsEvent
)

router = APIRouter()


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(None, description="JWT access token for authentication")
):
    """
    WebSocket endpoint for real-time updates
    
    Authentication: Pass JWT token as query parameter
    
    Message Format:
    {
        "type": "message_type",
        "data": { ... }
    }
    
    Supported message types:
    - subscribe: Subscribe to specific events
    - unsubscribe: Unsubscribe from events
    - subscribe_website: Subscribe to website-specific events
    - unsubscribe_website: Unsubscribe from website events
    - get_stats: Get connection statistics
    - get_recent_events: Get recent event history
    - ping: Heartbeat ping
    """
    if not token:
        await websocket.close(code=4001, reason="Missing authentication token")
        return
    
    await websocket_handler.handle_websocket(websocket, token)


@router.get("/stats")
async def get_websocket_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get WebSocket connection statistics"""
    stats = await websocket_manager.get_connection_stats()
    return {
        "websocket_stats": stats,
        "user_id": str(current_user.id)
    }


@router.get("/events/recent")
async def get_recent_events(
    limit: int = Query(50, ge=1, le=500, description="Number of recent events to retrieve"),
    event_types: Optional[List[str]] = Query(None, description="Filter by event types"),
    website_id: Optional[UUID] = Query(None, description="Filter by website ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get recent WebSocket events"""
    events = await websocket_manager.get_recent_events(limit)
    
    # Filter events based on parameters
    filtered_events = []
    for event in events:
        # Check event type filter
        if event_types and event.event_type not in event_types:
            continue
        
        # Check website filter
        if website_id and event.website_id != website_id:
            continue
        
        # Check user access (only show events for user's websites or global events)
        if event.website_id:
            # TODO: Verify user owns this website
            pass
        
        filtered_events.append(event.dict())
    
    return {
        "events": filtered_events,
        "total": len(filtered_events),
        "limit": limit
    }


@router.post("/events/test/visitor")
async def test_visitor_event(
    website_id: UUID,
    visitor_ip: str = "***********",
    country: str = "US",
    device_type: str = "desktop",
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Test visitor event (for development/testing)"""
    # TODO: Verify user owns the website
    
    # Create test visitor event
    visitor_event = VisitorEvent.create(
        event_type=EventType.VISITOR_CONNECTED,
        visitor_id=UUID("12345678-1234-5678-9012-123456789012"),
        ip_address=visitor_ip,
        website_id=website_id,
        country=country,
        device_type=device_type,
        page_url="/test-page",
        user_agent="Test User Agent"
    )
    
    # Broadcast event
    await websocket_manager.broadcast_event(visitor_event)
    
    return {
        "message": "Test visitor event sent",
        "event": visitor_event.dict()
    }


@router.post("/events/test/threat")
async def test_threat_event(
    website_id: UUID,
    threat_type: str = "sql_injection",
    severity: str = "high",
    source_ip: str = "***********00",
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Test threat event (for development/testing)"""
    # TODO: Verify user owns the website
    
    # Create test threat event
    threat_event = ThreatEvent.create(
        event_type=EventType.THREAT_DETECTED,
        threat_id=UUID("*************-8765-2109-************"),
        threat_type=threat_type,
        severity=severity,
        confidence_score=85,
        website_id=website_id,
        source_ip=source_ip,
        attack_vector="POST /login",
        description=f"Test {threat_type} attack detected"
    )
    
    # Broadcast event
    await websocket_manager.broadcast_event(threat_event)
    
    return {
        "message": "Test threat event sent",
        "event": threat_event.dict()
    }


@router.post("/events/test/scan")
async def test_scan_event(
    website_id: UUID,
    scan_type: str = "url_discovery",
    progress: int = 50,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Test scan event (for development/testing)"""
    # TODO: Verify user owns the website
    
    # Create test scan event
    scan_event = ScanEvent.create(
        event_type=EventType.SCAN_PROGRESS,
        scan_id=UUID("11111111-**************-************"),
        scan_type=scan_type,
        website_id=website_id,
        progress=progress,
        status="running",
        current_task=f"Scanning page {progress}/100",
        pages_scanned=progress
    )
    
    # Broadcast event
    await websocket_manager.broadcast_event(scan_event)
    
    return {
        "message": "Test scan event sent",
        "event": scan_event.dict()
    }


@router.post("/events/test/system")
async def test_system_event(
    message: str = "Test system alert",
    alert_level: str = "warning",
    website_id: Optional[UUID] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Test system event (for development/testing)"""
    # Create test system event
    system_event = SystemEvent.create(
        event_type=EventType.SYSTEM_ALERT,
        message=message,
        alert_level=alert_level,
        website_id=website_id,
        component="test_system",
        details={"test": True, "user_id": str(current_user.id)}
    )
    
    # Broadcast event
    await websocket_manager.broadcast_event(system_event)
    
    return {
        "message": "Test system event sent",
        "event": system_event.dict()
    }


@router.post("/events/test/analytics")
async def test_analytics_event(
    metric_type: str = "visitor_count",
    metric_value: int = 150,
    website_id: Optional[UUID] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Test analytics event (for development/testing)"""
    # Create test analytics event
    analytics_event = AnalyticsEvent.create(
        event_type=EventType.ANALYTICS_UPDATE,
        metric_type=metric_type,
        metric_value=metric_value,
        website_id=website_id,
        change_percentage=15.5,
        previous_value=130
    )
    
    # Broadcast event
    await websocket_manager.broadcast_event(analytics_event)
    
    return {
        "message": "Test analytics event sent",
        "event": analytics_event.dict()
    }


@router.get("/connections")
async def get_active_connections(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get information about active WebSocket connections"""
    stats = await websocket_manager.get_connection_stats()
    
    return {
        "active_connections": stats["active_connections"],
        "user_connections": stats.get("connections_by_user", {}),
        "website_subscriptions": stats.get("website_subscriptions", {}),
        "total_events_sent": stats["total_events_sent"]
    }
