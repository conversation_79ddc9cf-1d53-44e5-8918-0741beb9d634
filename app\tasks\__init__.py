"""
Background task processing with Ce<PERSON>y
"""

from .celery_app import celery_app
from .scan_tasks import (
    perform_url_discovery_task,
    perform_security_scan_task,
    perform_performance_scan_task,
    perform_full_scan_task
)
from .threat_tasks import (
    analyze_threat_patterns_task,
    update_threat_intelligence_task,
    train_ml_model_task
)
from .notification_tasks import (
    send_threat_alert_task,
    send_scan_report_task,
    send_weekly_report_task
)
from .maintenance_tasks import (
    cleanup_old_data_task,
    update_geolocation_data_task,
    backup_database_task
)

__all__ = [
    "celery_app",
    "perform_url_discovery_task",
    "perform_security_scan_task", 
    "perform_performance_scan_task",
    "perform_full_scan_task",
    "analyze_threat_patterns_task",
    "update_threat_intelligence_task",
    "train_ml_model_task",
    "send_threat_alert_task",
    "send_scan_report_task",
    "send_weekly_report_task",
    "cleanup_old_data_task",
    "update_geolocation_data_task",
    "backup_database_task"
]
