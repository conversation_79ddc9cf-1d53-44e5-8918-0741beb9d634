"""
Analytics schemas for request/response models
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any, Union
from uuid import UUID
from pydantic import BaseModel, Field
from enum import Enum


class TimeRange(str, Enum):
    """Time range enumeration for analytics"""
    HOUR = "hour"
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    QUARTER = "quarter"
    YEAR = "year"
    CUSTOM = "custom"


class MetricType(str, Enum):
    """Metric type enumeration"""
    VISITORS = "visitors"
    THREATS = "threats"
    SCANS = "scans"
    PERFORMANCE = "performance"
    SECURITY = "security"
    GEOGRAPHIC = "geographic"
    DEVICES = "devices"
    TRAFFIC = "traffic"


class AggregationType(str, Enum):
    """Aggregation type enumeration"""
    COUNT = "count"
    SUM = "sum"
    AVERAGE = "average"
    MIN = "min"
    MAX = "max"
    MEDIAN = "median"
    PERCENTILE = "percentile"


class AnalyticsQuery(BaseModel):
    """Analytics query parameters"""
    website_id: Optional[UUID] = None
    metric_type: MetricType
    time_range: TimeRange = TimeRange.DAY
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    aggregation: AggregationType = AggregationType.COUNT
    group_by: Optional[List[str]] = None
    filters: Optional[Dict[str, Any]] = None
    limit: Optional[int] = Field(None, ge=1, le=1000)


class DashboardMetrics(BaseModel):
    """Main dashboard metrics"""
    # Overview metrics
    total_websites: int = 0
    active_websites: int = 0
    total_visitors: int = 0
    unique_visitors: int = 0
    total_threats: int = 0
    blocked_threats: int = 0
    total_scans: int = 0
    active_scans: int = 0
    
    # Time-based metrics
    visitors_today: int = 0
    visitors_yesterday: int = 0
    threats_today: int = 0
    threats_yesterday: int = 0
    
    # Performance metrics
    avg_response_time: float = 0.0
    uptime_percentage: float = 0.0
    security_score: float = 0.0
    
    # Growth metrics
    visitor_growth: float = 0.0
    threat_growth: float = 0.0
    
    # Real-time metrics
    active_visitors_now: int = 0
    threats_last_hour: int = 0
    
    # Last updated
    last_updated: datetime


class VisitorAnalytics(BaseModel):
    """Visitor analytics data"""
    total_visitors: int
    unique_visitors: int
    returning_visitors: int
    new_visitors: int
    bounce_rate: float
    avg_session_duration: float
    page_views: int
    avg_pages_per_session: float
    
    # Time series data
    hourly_visitors: List[Dict[str, Union[datetime, int]]]
    daily_visitors: List[Dict[str, Union[date, int]]]
    
    # Geographic data
    top_countries: List[Dict[str, Any]]
    top_regions: List[Dict[str, Any]]
    top_cities: List[Dict[str, Any]]
    
    # Device data
    device_breakdown: Dict[str, int]
    browser_breakdown: Dict[str, int]
    os_breakdown: Dict[str, int]
    
    # Traffic sources
    referrer_breakdown: Dict[str, int]
    search_engines: Dict[str, int]
    
    # Risk assessment
    risk_distribution: Dict[str, int]
    vpn_usage: int
    proxy_usage: int
    tor_usage: int


class ThreatAnalytics(BaseModel):
    """Threat analytics data"""
    total_threats: int
    blocked_threats: int
    false_positives: int
    avg_confidence_score: float
    
    # Severity breakdown
    critical_threats: int
    high_threats: int
    medium_threats: int
    low_threats: int
    
    # Type breakdown
    threats_by_type: Dict[str, int]
    
    # Time series data
    hourly_threats: List[Dict[str, Union[datetime, int]]]
    daily_threats: List[Dict[str, Union[date, int]]]
    
    # Geographic data
    threats_by_country: List[Dict[str, Any]]
    top_threat_sources: List[Dict[str, Any]]
    
    # Attack patterns
    top_attack_vectors: List[Dict[str, Any]]
    peak_attack_times: List[Dict[str, Any]]
    
    # Mitigation effectiveness
    blocking_effectiveness: float
    response_time: float
    
    # Trends
    threat_trend: str  # "increasing", "decreasing", "stable"
    severity_trend: Dict[str, str]


class SecurityAnalytics(BaseModel):
    """Security analytics data"""
    overall_security_score: float
    vulnerability_count: int
    fixed_vulnerabilities: int
    open_vulnerabilities: int
    
    # Vulnerability breakdown
    vulnerabilities_by_severity: Dict[str, int]
    vulnerabilities_by_type: Dict[str, int]
    
    # Compliance status
    compliance_scores: Dict[str, float]
    
    # SSL/TLS status
    ssl_certificates: Dict[str, int]  # valid, expired, expiring_soon
    ssl_grade_distribution: Dict[str, int]
    
    # Security headers
    security_headers_compliance: Dict[str, float]
    
    # Scan results
    last_scan_results: Dict[str, Any]
    scan_frequency: Dict[str, int]
    
    # Recommendations
    top_recommendations: List[str]
    priority_fixes: List[Dict[str, Any]]


class PerformanceAnalytics(BaseModel):
    """Performance analytics data"""
    avg_response_time: float
    median_response_time: float
    p95_response_time: float
    p99_response_time: float
    
    # Uptime metrics
    uptime_percentage: float
    downtime_incidents: int
    mttr: float  # Mean Time To Recovery
    
    # Page performance
    page_load_times: Dict[str, float]
    slowest_pages: List[Dict[str, Any]]
    
    # Time series data
    hourly_performance: List[Dict[str, Union[datetime, float]]]
    daily_performance: List[Dict[str, Union[date, float]]]
    
    # Error rates
    error_rate: float
    status_code_distribution: Dict[str, int]
    
    # Resource usage
    bandwidth_usage: float
    request_volume: int
    
    # Geographic performance
    performance_by_region: List[Dict[str, Any]]


class GeographicAnalytics(BaseModel):
    """Geographic analytics data"""
    # Visitor distribution
    visitor_distribution: List[Dict[str, Any]]
    
    # Threat distribution
    threat_distribution: List[Dict[str, Any]]
    
    # Performance by location
    performance_by_location: List[Dict[str, Any]]
    
    # Risk assessment by location
    risk_by_location: List[Dict[str, Any]]
    
    # Top countries/regions/cities
    top_countries: List[Dict[str, Any]]
    top_regions: List[Dict[str, Any]]
    top_cities: List[Dict[str, Any]]
    
    # Coordinates for mapping
    visitor_coordinates: List[Dict[str, float]]
    threat_coordinates: List[Dict[str, float]]


class RealtimeAnalytics(BaseModel):
    """Real-time analytics data"""
    timestamp: datetime
    
    # Current activity
    active_visitors: int
    active_sessions: int
    current_threats: int
    active_scans: int
    
    # Recent activity (last 5 minutes)
    recent_visitors: List[Dict[str, Any]]
    recent_threats: List[Dict[str, Any]]
    recent_page_views: List[Dict[str, Any]]
    
    # Live metrics
    requests_per_minute: int
    threats_per_minute: int
    avg_response_time_live: float
    
    # Geographic activity
    live_visitor_map: List[Dict[str, Any]]
    live_threat_map: List[Dict[str, Any]]
    
    # System status
    system_health: Dict[str, Any]
    api_status: Dict[str, str]


class AnalyticsReport(BaseModel):
    """Comprehensive analytics report"""
    report_id: UUID
    website_id: Optional[UUID]
    report_type: str
    time_range: TimeRange
    start_date: datetime
    end_date: datetime
    
    # Report sections
    executive_summary: Dict[str, Any]
    visitor_analytics: VisitorAnalytics
    threat_analytics: ThreatAnalytics
    security_analytics: SecurityAnalytics
    performance_analytics: PerformanceAnalytics
    geographic_analytics: GeographicAnalytics
    
    # Recommendations
    recommendations: List[str]
    action_items: List[Dict[str, Any]]
    
    # Metadata
    generated_at: datetime
    generated_by: UUID
    format: str = "json"


class AnalyticsExport(BaseModel):
    """Analytics export configuration"""
    website_id: Optional[UUID] = None
    metrics: List[MetricType]
    time_range: TimeRange
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    format: str = Field("csv", description="Export format: csv, json, xlsx, pdf")
    include_charts: bool = False
    email_recipients: Optional[List[str]] = None


class AnalyticsAlert(BaseModel):
    """Analytics-based alert configuration"""
    name: str = Field(..., max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    metric_type: MetricType
    threshold_value: float
    comparison_operator: str = Field(..., description="gt, lt, eq, gte, lte")
    time_window: int = Field(..., description="Time window in minutes")
    is_active: bool = True
    notification_channels: List[str] = []
    cooldown_period: int = Field(300, description="Cooldown period in seconds")


class AnalyticsAlertResponse(AnalyticsAlert):
    """Analytics alert response"""
    id: UUID
    website_id: Optional[UUID]
    last_triggered: Optional[datetime] = None
    trigger_count: int = 0
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
