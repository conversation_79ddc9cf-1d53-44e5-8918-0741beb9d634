# ProSecurity Monitor - FastAPI Backend

A comprehensive web security monitoring platform built with FastAPI, designed for Railway deployment with Docker containerization and GitHub CI/CD integration.

## 🚀 Features

- **Real-time Threat Detection** - Monitor and block security threats instantly
- **Advanced Visitor Tracking** - Geographic, device, and behavioral analytics
- **URL Discovery & Mapping** - Comprehensive website crawling and analysis
- **Database Structure Analysis** - Security vulnerability scanning
- **Intelligent Blocking System** - Multi-level blocking with dynamic rules
- **WebSocket Real-time Updates** - Live monitoring and notifications

## 🏗️ Architecture

- **FastAPI** - Modern, fast web framework
- **PostgreSQL** - Primary database with Railway integration
- **Redis** - Caching and real-time features
- **Celery** - Background task processing
- **Docker** - Containerization for consistent deployment
- **Railway** - Cloud hosting platform

## 🛠️ Local Development Setup

### Prerequisites

- Python 3.11+
- Docker and Docker Compose
- Git

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Prosecurity-monitor-backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Setup environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Run with Docker Compose**
   ```bash
   docker-compose up -d
   ```

6. **Access the application**
   - API: http://localhost:8000
   - API Docs: http://localhost:8000/docs
   - Flower (Celery): http://localhost:5555

## 🚂 Railway Deployment

### Automatic Deployment

The project is configured for automatic deployment to Railway via GitHub Actions.

1. **Fork/Clone this repository**

2. **Create Railway Project**
   ```bash
   # Install Railway CLI
   npm install -g @railway/cli
   
   # Login to Railway
   railway login
   
   # Create new project
   railway init
   ```

3. **Add Environment Variables in Railway**
   - `SECRET_KEY` - Your secret key for JWT tokens
   - `GEOLOCATION_API_KEY` - API key for geolocation services
   - `THREAT_INTELLIGENCE_API_KEY` - API key for threat intelligence
   - `SENTRY_DSN` - Optional: Sentry DSN for error tracking

4. **Add Railway Services**
   ```bash
   # Add PostgreSQL
   railway add postgresql
   
   # Add Redis
   railway add redis
   ```

5. **Setup GitHub Secrets**
   Add these secrets to your GitHub repository:
   - `RAILWAY_TOKEN` - Your Railway API token
   - `RAILWAY_SERVICE_ID` - Your Railway service ID

6. **Deploy**
   ```bash
   git push origin main
   ```

### Manual Deployment

```bash
# Deploy directly to Railway
railway up
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `ENVIRONMENT` | Environment (development/production) | development |
| `SECRET_KEY` | JWT secret key | Required |
| `DATABASE_URL` | PostgreSQL connection string | Auto-set by Railway |
| `REDIS_URL` | Redis connection string | Auto-set by Railway |
| `ALLOWED_ORIGINS` | CORS allowed origins | localhost:3000 |
| `GEOLOCATION_API_KEY` | Geolocation service API key | Optional |
| `SENTRY_DSN` | Sentry error tracking DSN | Optional |

### Railway-Specific Configuration

The application automatically detects Railway environment and uses:
- `DATABASE_PRIVATE_URL` for better performance
- `REDIS_PRIVATE_URL` for internal communication
- `PORT` environment variable set by Railway
- Automatic SSL/TLS termination

## 📊 API Documentation

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh token
- `GET /api/v1/auth/me` - Get current user

### Website Management
- `GET /api/v1/websites` - List websites
- `POST /api/v1/websites` - Add website
- `PUT /api/v1/websites/{id}` - Update website
- `DELETE /api/v1/websites/{id}` - Delete website

### Visitor Tracking
- `GET /api/v1/visitors` - Get visitors
- `GET /api/v1/visitors/analytics` - Visitor analytics
- `GET /api/v1/visitors/realtime` - Real-time feed

### URL Discovery
- `POST /api/v1/discovery/scan` - Start URL scan
- `GET /api/v1/discovery/results/{scan_id}` - Get results

### Database Analysis
- `POST /api/v1/database/scan` - Start DB scan
- `GET /api/v1/database/schema/{scan_id}` - Get schema

## 🧪 Testing

```bash
# Run tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest tests/test_auth.py -v
```

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Rate limiting middleware
- CORS protection
- Security headers
- Input validation
- SQL injection prevention

## 📈 Monitoring

- Health check endpoint: `/health`
- Prometheus metrics (planned)
- Structured logging with structlog
- Error tracking with Sentry

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Check the API documentation at `/docs`
- Review the Railway deployment logs
