"""
Website management endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.schemas.website import (
    WebsiteCreate, WebsiteUpdate, WebsiteResponse, WebsiteDetailResponse,
    WebsiteListResponse, WebsiteHealthCheck, WebsiteVerification,
    WebsiteVerificationResponse, WebsiteScanTrigger
)
from app.services.website_service import WebsiteService

router = APIRouter()
website_service = WebsiteService()


@router.get("/", response_model=WebsiteListResponse)
async def list_websites(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List all websites for the current user with pagination and filtering"""
    return await website_service.get_user_websites(
        db,
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        is_active=is_active
    )


@router.post("/", response_model=WebsiteResponse, status_code=status.HTTP_201_CREATED)
async def create_website(
    website_data: WebsiteCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new website for monitoring"""
    website = await website_service.create_website(
        db,
        website_data=website_data,
        user=current_user
    )
    return WebsiteResponse.from_orm(website)


@router.get("/{website_id}", response_model=WebsiteDetailResponse)
async def get_website(
    website_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed website information"""
    return await website_service.get_website_detail(
        db,
        website_id=website_id,
        user_id=current_user.id
    )


@router.put("/{website_id}", response_model=WebsiteResponse)
async def update_website(
    website_id: UUID,
    website_data: WebsiteUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update website configuration"""
    website = await website_service.update_website(
        db,
        website_id=website_id,
        website_data=website_data,
        user_id=current_user.id
    )
    return WebsiteResponse.from_orm(website)


@router.delete("/{website_id}", response_model=WebsiteResponse)
async def delete_website(
    website_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a website and all associated data"""
    website = await website_service.delete_website(
        db,
        website_id=website_id,
        user_id=current_user.id
    )
    return WebsiteResponse.from_orm(website)


@router.post("/{website_id}/health-check", response_model=WebsiteHealthCheck)
async def perform_health_check(
    website_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Perform a health check on the website"""
    return await website_service.perform_health_check(
        db,
        website_id=website_id,
        user_id=current_user.id
    )


@router.post("/{website_id}/verify", response_model=WebsiteVerificationResponse)
async def verify_website(
    website_id: UUID,
    verification_data: WebsiteVerification,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Verify website ownership"""
    return await website_service.verify_website(
        db,
        website_id=website_id,
        verification_data=verification_data,
        user_id=current_user.id
    )


@router.post("/{website_id}/scan")
async def trigger_website_scan(
    website_id: UUID,
    scan_data: WebsiteScanTrigger,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Trigger a manual scan of the website"""
    # This will be implemented when scan service is ready
    return {
        "message": "Scan triggered successfully",
        "website_id": website_id,
        "scan_type": scan_data.scan_type,
        "priority": scan_data.priority
    }
