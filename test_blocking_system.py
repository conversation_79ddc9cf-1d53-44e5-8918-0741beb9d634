#!/usr/bin/env python3
"""
Quick test script for the blocking system
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.blocking_service import blocking_service
from app.schemas.blocking import BlockingRuleCreate, IPBlockRequest
from app.models.blocking import BlockingRuleType, BlockingAction


async def test_blocking_system():
    """Test the blocking system functionality"""
    print("🔒 Testing ProSecurity Monitor Blocking System")
    print("=" * 50)
    
    # Create a database session
    db: Session = SessionLocal()
    
    try:
        # Test 1: Create a blocking rule
        print("\n1. Testing blocking rule creation...")
        
        rule_data = BlockingRuleCreate(
            name="Test IP Block",
            description="Test blocking rule for IP address",
            rule_type=BlockingRuleType.IP_ADDRESS,
            action=BlockingAction.BLOCK,
            rule_value="*************",
            priority=50,
            is_active=True
        )
        
        # Mock user ID for testing
        from uuid import uuid4
        test_user_id = uuid4()
        
        try:
            rule = await blocking_service.create_blocking_rule(
                db, rule_data=rule_data, user_id=test_user_id
            )
            print(f"✅ Created blocking rule: {rule.name} (ID: {rule.id})")
        except Exception as e:
            print(f"❌ Failed to create blocking rule: {e}")
        
        # Test 2: Test IP blocking
        print("\n2. Testing IP blocking...")
        
        ip_block_request = IPBlockRequest(
            ip_address="********",
            reason="Suspicious activity detected",
            is_temporary=True
        )
        
        try:
            ip_rule = await blocking_service.block_ip_address(
                db, request_data=ip_block_request, user_id=test_user_id
            )
            print(f"✅ Blocked IP address: {ip_rule.rule_value}")
        except Exception as e:
            print(f"❌ Failed to block IP: {e}")
        
        # Test 3: Test rule matching
        print("\n3. Testing rule matching...")
        
        # Mock website ID
        test_website_id = uuid4()
        
        # Test request data
        request_data = {
            'ip_address': '*************',
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
            'country': 'US'
        }
        
        try:
            should_block, matched_rule, action = await blocking_service.check_blocking_rules(
                db, website_id=test_website_id, request_data=request_data
            )
            
            if should_block:
                print(f"✅ Request would be blocked by rule: {matched_rule.name}")
            else:
                print("✅ Request would be allowed")
                
        except Exception as e:
            print(f"❌ Failed to check blocking rules: {e}")
        
        # Test 4: Get user blocking rules
        print("\n4. Testing rule retrieval...")
        
        try:
            rules_result = await blocking_service.get_user_blocking_rules(
                db, user_id=test_user_id, skip=0, limit=10
            )
            print(f"✅ Retrieved {len(rules_result['rules'])} blocking rules")
            
            for rule in rules_result['rules']:
                print(f"   - {rule.name}: {rule.rule_type} = {rule.rule_value}")
                
        except Exception as e:
            print(f"❌ Failed to retrieve rules: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 Blocking system test completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        
    finally:
        db.close()


if __name__ == "__main__":
    # Run the test
    asyncio.run(test_blocking_system())
