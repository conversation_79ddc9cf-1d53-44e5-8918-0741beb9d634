"""
Database analysis service
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from app.services.base_service import BaseService
from app.services.event_service import event_service
from app.models.scan import Scan
from app.models.website import Website
from app.schemas.scan import ScanCreate

logger = logging.getLogger(__name__)


class DatabaseService(BaseService[Scan, ScanCreate, Dict]):
    """Service for database structure analysis"""
    
    def __init__(self):
        super().__init__(Scan)
    
    async def test_connection(
        self,
        db: Session,
        *,
        connection_data: Dict[str, Any],
        user_id: UUID
    ) -> Dict[str, Any]:
        """Test database connection"""
        try:
            # Mock connection test
            # In real implementation, would test actual database connection
            db_type = connection_data.get("type", "postgresql")
            host = connection_data.get("host", "localhost")
            port = connection_data.get("port", 5432)
            database = connection_data.get("database", "")
            
            # Simulate connection test
            await asyncio.sleep(0.5)  # Simulate connection time
            
            return {
                "status": "success",
                "message": "Database connection successful",
                "connection_info": {
                    "type": db_type,
                    "host": host,
                    "port": port,
                    "database": database,
                    "version": "14.2",  # Mock version
                    "response_time_ms": 150
                },
                "tested_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return {
                "status": "failed",
                "message": f"Connection failed: {str(e)}",
                "tested_at": datetime.utcnow().isoformat()
            }
    
    async def start_database_analysis(
        self,
        db: Session,
        *,
        website_id: UUID,
        connection_data: Dict[str, Any],
        user_id: UUID,
        scan_type: str = "full"
    ) -> Scan:
        """Start database structure analysis"""
        # Verify website ownership
        website = db.query(Website).filter(
            and_(Website.id == website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise ValueError("Website not found")
        
        # Create scan record
        scan_data = ScanCreate(
            website_id=website_id,
            scan_type="database_analysis",
            status="pending",
            scan_config={
                "scan_type": scan_type,
                "connection_type": connection_data.get("type", "postgresql"),
                "include_data_analysis": scan_type == "full",
                "security_checks": True
            }
        )
        
        scan = await self.create(db, obj_in=scan_data)
        
        # Emit event
        asyncio.create_task(event_service.emit_scan_event(
            event_type="scan_started",
            scan_id=scan.id,
            website_id=website_id,
            scan_type="database_analysis",
            message="Database analysis scan started"
        ))
        
        # Start background scan task (mock)
        # In real implementation, would start actual database analysis
        asyncio.create_task(self._mock_database_analysis(db, scan.id, connection_data))
        
        return scan
    
    async def _mock_database_analysis(
        self,
        db: Session,
        scan_id: UUID,
        connection_data: Dict[str, Any]
    ):
        """Mock database analysis (for demonstration)"""
        try:
            # Simulate analysis time
            await asyncio.sleep(2)
            
            # Update scan with mock results
            scan = db.query(Scan).filter(Scan.id == scan_id).first()
            if scan:
                scan.status = "completed"
                scan.completed_at = datetime.utcnow()
                scan.results = {
                    "tables": [
                        {
                            "name": "users",
                            "columns": [
                                {"name": "id", "type": "integer", "primary_key": True},
                                {"name": "email", "type": "varchar", "nullable": False},
                                {"name": "password", "type": "varchar", "nullable": False},
                                {"name": "created_at", "type": "timestamp", "nullable": False}
                            ],
                            "row_count": 1250,
                            "indexes": ["idx_users_email"]
                        },
                        {
                            "name": "orders",
                            "columns": [
                                {"name": "id", "type": "integer", "primary_key": True},
                                {"name": "user_id", "type": "integer", "foreign_key": "users.id"},
                                {"name": "total", "type": "decimal", "nullable": False},
                                {"name": "status", "type": "varchar", "nullable": False}
                            ],
                            "row_count": 5420,
                            "indexes": ["idx_orders_user_id", "idx_orders_status"]
                        }
                    ],
                    "relationships": [
                        {
                            "from_table": "orders",
                            "from_column": "user_id",
                            "to_table": "users",
                            "to_column": "id",
                            "relationship_type": "many_to_one"
                        }
                    ],
                    "vulnerabilities": [
                        {
                            "type": "weak_password_policy",
                            "severity": "medium",
                            "table": "users",
                            "column": "password",
                            "description": "Password column may not have sufficient constraints",
                            "recommendation": "Implement strong password policy"
                        },
                        {
                            "type": "missing_encryption",
                            "severity": "high",
                            "table": "users",
                            "column": "email",
                            "description": "Email addresses are stored in plain text",
                            "recommendation": "Consider encrypting sensitive data"
                        }
                    ],
                    "security_score": 75,
                    "total_tables": 2,
                    "total_columns": 8,
                    "total_relationships": 1
                }
                db.commit()
                
                # Emit completion event
                asyncio.create_task(event_service.emit_scan_event(
                    event_type="scan_completed",
                    scan_id=scan.id,
                    website_id=scan.website_id,
                    scan_type="database_analysis",
                    message="Database analysis completed"
                ))
                
        except Exception as e:
            logger.error(f"Error in mock database analysis: {e}")
    
    async def get_database_schema(
        self,
        db: Session,
        *,
        scan_id: UUID,
        user_id: UUID
    ) -> Dict[str, Any]:
        """Get database schema from scan results"""
        scan = db.query(Scan).filter(
            and_(
                Scan.id == scan_id,
                Scan.website.has(Website.user_id == user_id),
                Scan.scan_type == "database_analysis"
            )
        ).first()
        
        if not scan:
            raise ValueError("Database scan not found")
        
        if not scan.results:
            return {"tables": [], "total_tables": 0}
        
        return {
            "tables": scan.results.get("tables", []),
            "total_tables": scan.results.get("total_tables", 0),
            "total_columns": scan.results.get("total_columns", 0),
            "scan_date": scan.completed_at.isoformat() if scan.completed_at else None
        }
    
    async def get_database_vulnerabilities(
        self,
        db: Session,
        *,
        scan_id: UUID,
        user_id: UUID,
        severity: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Dict[str, Any]:
        """Get database vulnerabilities from scan"""
        scan = db.query(Scan).filter(
            and_(
                Scan.id == scan_id,
                Scan.website.has(Website.user_id == user_id),
                Scan.scan_type == "database_analysis"
            )
        ).first()
        
        if not scan:
            raise ValueError("Database scan not found")
        
        if not scan.results:
            return {"vulnerabilities": [], "total": 0}
        
        vulnerabilities = scan.results.get("vulnerabilities", [])
        
        # Filter by severity if specified
        if severity:
            vulnerabilities = [v for v in vulnerabilities if v.get("severity") == severity]
        
        total = len(vulnerabilities)
        paginated_vulns = vulnerabilities[skip:skip + limit]
        
        return {
            "vulnerabilities": paginated_vulns,
            "total": total,
            "page": (skip // limit) + 1,
            "per_page": limit,
            "has_next": skip + limit < total,
            "has_prev": skip > 0,
            "security_score": scan.results.get("security_score", 0)
        }
    
    async def get_database_relationships(
        self,
        db: Session,
        *,
        scan_id: UUID,
        user_id: UUID,
        table_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get database relationships from scan"""
        scan = db.query(Scan).filter(
            and_(
                Scan.id == scan_id,
                Scan.website.has(Website.user_id == user_id),
                Scan.scan_type == "database_analysis"
            )
        ).first()
        
        if not scan:
            raise ValueError("Database scan not found")
        
        if not scan.results:
            return {"relationships": [], "total": 0}
        
        relationships = scan.results.get("relationships", [])
        
        # Filter by table name if specified
        if table_name:
            relationships = [
                r for r in relationships 
                if r.get("from_table") == table_name or r.get("to_table") == table_name
            ]
        
        return {
            "relationships": relationships,
            "total": len(relationships),
            "table_filter": table_name
        }
    
    async def get_database_tables(
        self,
        db: Session,
        *,
        scan_id: UUID,
        user_id: UUID,
        include_columns: bool = True,
        skip: int = 0,
        limit: int = 100
    ) -> Dict[str, Any]:
        """Get database tables from scan"""
        scan = db.query(Scan).filter(
            and_(
                Scan.id == scan_id,
                Scan.website.has(Website.user_id == user_id),
                Scan.scan_type == "database_analysis"
            )
        ).first()
        
        if not scan:
            raise ValueError("Database scan not found")
        
        if not scan.results:
            return {"tables": [], "total": 0}
        
        tables = scan.results.get("tables", [])
        
        # Remove column details if not requested
        if not include_columns:
            tables = [
                {k: v for k, v in table.items() if k != "columns"}
                for table in tables
            ]
        
        total = len(tables)
        paginated_tables = tables[skip:skip + limit]
        
        return {
            "tables": paginated_tables,
            "total": total,
            "page": (skip // limit) + 1,
            "per_page": limit,
            "has_next": skip + limit < total,
            "has_prev": skip > 0
        }
    
    async def get_security_report(
        self,
        db: Session,
        *,
        scan_id: UUID,
        user_id: UUID,
        format: str = "json"
    ) -> Any:
        """Get comprehensive database security report"""
        scan = db.query(Scan).filter(
            and_(
                Scan.id == scan_id,
                Scan.website.has(Website.user_id == user_id),
                Scan.scan_type == "database_analysis"
            )
        ).first()
        
        if not scan:
            raise ValueError("Database scan not found")
        
        if not scan.results:
            return {"error": "No scan results available"}
        
        # Generate comprehensive report
        report = {
            "scan_info": {
                "scan_id": str(scan_id),
                "scan_date": scan.completed_at.isoformat() if scan.completed_at else None,
                "scan_type": scan.scan_type,
                "status": scan.status
            },
            "summary": {
                "total_tables": scan.results.get("total_tables", 0),
                "total_columns": scan.results.get("total_columns", 0),
                "total_relationships": scan.results.get("total_relationships", 0),
                "security_score": scan.results.get("security_score", 0),
                "vulnerabilities_found": len(scan.results.get("vulnerabilities", []))
            },
            "vulnerabilities": scan.results.get("vulnerabilities", []),
            "recommendations": [
                "Implement strong password policies",
                "Encrypt sensitive data at rest",
                "Regular security audits",
                "Implement proper access controls",
                "Keep database software updated"
            ],
            "generated_at": datetime.utcnow().isoformat()
        }
        
        if format == "pdf":
            # In real implementation, would generate PDF
            return b"Mock PDF content"
        
        return report


# Global database service instance
database_service = DatabaseService()
