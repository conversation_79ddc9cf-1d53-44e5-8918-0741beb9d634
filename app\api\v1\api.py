"""
Main API router for v1 endpoints
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, websites, visitors, discovery, database, threats, analytics

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(websites.router, prefix="/websites", tags=["websites"])
api_router.include_router(visitors.router, prefix="/visitors", tags=["visitors"])
api_router.include_router(discovery.router, prefix="/discovery", tags=["url-discovery"])
api_router.include_router(database.router, prefix="/database", tags=["database-analysis"])
api_router.include_router(threats.router, prefix="/threats", tags=["threat-detection"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
