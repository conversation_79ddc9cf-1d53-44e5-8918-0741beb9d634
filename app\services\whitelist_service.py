"""
Whitelist service for managing allowed entries
"""

import asyncio
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from app.services.base_service import BaseService
from app.services.event_service import event_service
from app.models.blocking import WhitelistEntry
from app.schemas.blocking import (
    WhitelistEntryCreate, WhitelistEntryUpdate, WhitelistEntryResponse
)

logger = logging.getLogger(__name__)


class WhitelistService(BaseService[WhitelistEntry, WhitelistEntryCreate, WhitelistEntryUpdate]):
    """Service for managing whitelist entries"""
    
    def __init__(self):
        super().__init__(WhitelistEntry)
    
    async def create_whitelist_entry(
        self,
        db: Session,
        *,
        entry_data: WhitelistEntryCreate,
        user_id: UUID
    ) -> WhitelistEntry:
        """Create a new whitelist entry"""
        try:
            # Create entry
            entry_dict = entry_data.dict()
            entry_dict['user_id'] = user_id
            entry_dict['created_by'] = f"user_{user_id}"
            
            entry = await self.create(db, obj_in=entry_dict)
            
            # Emit event
            asyncio.create_task(event_service.emit_system_event(
                event_type="whitelist_entry_created",
                message=f"Whitelist entry created for {entry.entry_type}: {entry.entry_value}",
                website_id=entry.website_id,
                component="blocking_system",
                details={
                    "entry_id": str(entry.id),
                    "entry_type": entry.entry_type,
                    "entry_value": entry.entry_value
                }
            ))
            
            return entry
            
        except Exception as e:
            logger.error(f"Error creating whitelist entry: {e}")
            raise
    
    async def get_user_whitelist_entries(
        self,
        db: Session,
        *,
        user_id: UUID,
        website_id: Optional[UUID] = None,
        entry_type: Optional[str] = None,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Dict[str, Any]:
        """Get whitelist entries for a user with filtering"""
        query = db.query(WhitelistEntry).filter(WhitelistEntry.user_id == user_id)
        
        if website_id:
            query = query.filter(WhitelistEntry.website_id == website_id)
        if entry_type:
            query = query.filter(WhitelistEntry.entry_type == entry_type)
        if is_active is not None:
            query = query.filter(WhitelistEntry.is_active == is_active)
        
        # Filter out expired entries unless specifically requested
        if is_active is None or is_active:
            query = query.filter(
                or_(
                    WhitelistEntry.expires_at.is_(None),
                    WhitelistEntry.expires_at > datetime.utcnow()
                )
            )
        
        # Get total count
        total = query.count()
        
        # Apply pagination and ordering
        entries = query.order_by(desc(WhitelistEntry.created_at))\
                      .offset(skip).limit(limit).all()
        
        return {
            "entries": entries,
            "total": total,
            "page": (skip // limit) + 1,
            "per_page": limit,
            "has_next": skip + limit < total,
            "has_prev": skip > 0
        }
    
    async def update_whitelist_entry(
        self,
        db: Session,
        *,
        entry_id: UUID,
        entry_data: WhitelistEntryUpdate,
        user_id: UUID
    ) -> WhitelistEntry:
        """Update a whitelist entry"""
        entry = await self.get(db, id=entry_id)
        if not entry or entry.user_id != user_id:
            raise ValueError("Whitelist entry not found")
        
        updated_entry = await self.update(db, db_obj=entry, obj_in=entry_data)
        
        # Emit event
        asyncio.create_task(event_service.emit_system_event(
            event_type="whitelist_entry_updated",
            message=f"Whitelist entry updated: {updated_entry.entry_type}: {updated_entry.entry_value}",
            website_id=updated_entry.website_id,
            component="blocking_system",
            details={"entry_id": str(entry_id)}
        ))
        
        return updated_entry
    
    async def delete_whitelist_entry(
        self,
        db: Session,
        *,
        entry_id: UUID,
        user_id: UUID
    ) -> bool:
        """Delete a whitelist entry"""
        entry = await self.get(db, id=entry_id)
        if not entry or entry.user_id != user_id:
            raise ValueError("Whitelist entry not found")
        
        await self.delete(db, id=entry_id)
        
        # Emit event
        asyncio.create_task(event_service.emit_system_event(
            event_type="whitelist_entry_deleted",
            message=f"Whitelist entry deleted: {entry.entry_type}: {entry.entry_value}",
            website_id=entry.website_id,
            component="blocking_system",
            details={"entry_id": str(entry_id)}
        ))
        
        return True
    
    async def add_ip_to_whitelist(
        self,
        db: Session,
        *,
        website_id: UUID,
        ip_address: str,
        description: Optional[str] = None,
        user_id: UUID
    ) -> WhitelistEntry:
        """Quick method to add IP to whitelist"""
        entry_data = WhitelistEntryCreate(
            website_id=website_id,
            entry_type="ip_address",
            entry_value=ip_address,
            description=description or f"Whitelisted IP: {ip_address}"
        )
        
        return await self.create_whitelist_entry(db, entry_data=entry_data, user_id=user_id)
    
    async def add_country_to_whitelist(
        self,
        db: Session,
        *,
        website_id: UUID,
        country_code: str,
        description: Optional[str] = None,
        user_id: UUID
    ) -> WhitelistEntry:
        """Quick method to add country to whitelist"""
        entry_data = WhitelistEntryCreate(
            website_id=website_id,
            entry_type="country",
            entry_value=country_code.upper(),
            description=description or f"Whitelisted country: {country_code}"
        )
        
        return await self.create_whitelist_entry(db, entry_data=entry_data, user_id=user_id)
    
    async def check_whitelist(
        self,
        db: Session,
        *,
        website_id: UUID,
        check_data: Dict[str, Any]
    ) -> bool:
        """Check if request data matches any whitelist entry"""
        try:
            # Get active whitelist entries for website
            entries = db.query(WhitelistEntry).filter(
                and_(
                    WhitelistEntry.website_id == website_id,
                    WhitelistEntry.is_active == True,
                    or_(
                        WhitelistEntry.expires_at.is_(None),
                        WhitelistEntry.expires_at > datetime.utcnow()
                    )
                )
            ).all()
            
            for entry in entries:
                if self._entry_matches(entry, check_data):
                    # Update usage statistics
                    entry.usage_count += 1
                    entry.last_used = datetime.utcnow()
                    db.commit()
                    
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking whitelist: {e}")
            return False
    
    def _entry_matches(self, entry: WhitelistEntry, check_data: Dict[str, Any]) -> bool:
        """Check if whitelist entry matches the check data"""
        if entry.entry_type == "ip_address":
            return check_data.get('ip_address') == entry.entry_value
        elif entry.entry_type == "country":
            return check_data.get('country') == entry.entry_value
        elif entry.entry_type == "user_agent":
            user_agent = check_data.get('user_agent', '')
            return entry.entry_value.lower() in user_agent.lower()
        elif entry.entry_type == "domain":
            return check_data.get('domain') == entry.entry_value
        
        return False
    
    async def get_whitelist_analytics(
        self,
        db: Session,
        *,
        user_id: UUID,
        website_id: Optional[UUID] = None
    ) -> Dict[str, Any]:
        """Get whitelist analytics"""
        try:
            query = db.query(WhitelistEntry).filter(WhitelistEntry.user_id == user_id)
            
            if website_id:
                query = query.filter(WhitelistEntry.website_id == website_id)
            
            # Basic counts
            total_entries = query.count()
            active_entries = query.filter(WhitelistEntry.is_active == True).count()
            
            # Entries by type
            type_counts = {}
            for entry_type in ["ip_address", "country", "user_agent", "domain"]:
                count = query.filter(WhitelistEntry.entry_type == entry_type).count()
                if count > 0:
                    type_counts[entry_type] = count
            
            # Most used entries
            most_used = query.filter(WhitelistEntry.usage_count > 0)\
                            .order_by(desc(WhitelistEntry.usage_count))\
                            .limit(10).all()
            
            most_used_entries = [
                {
                    "entry_type": entry.entry_type,
                    "entry_value": entry.entry_value,
                    "usage_count": entry.usage_count,
                    "last_used": entry.last_used.isoformat() if entry.last_used else None
                }
                for entry in most_used
            ]
            
            return {
                "total_entries": total_entries,
                "active_entries": active_entries,
                "entries_by_type": type_counts,
                "most_used_entries": most_used_entries
            }
            
        except Exception as e:
            logger.error(f"Error getting whitelist analytics: {e}")
            return {
                "total_entries": 0,
                "active_entries": 0,
                "entries_by_type": {},
                "most_used_entries": []
            }


# Global whitelist service instance
whitelist_service = WhitelistService()
