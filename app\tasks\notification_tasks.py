"""
Background tasks for notifications and reporting
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List
from uuid import UUID

from celery import current_task
from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from app.tasks.celery_app import celery_app
from app.core.database import SessionLocal
from app.models.threat import Threat
from app.models.visitor import Visitor
from app.models.website import Website
from app.models.user import User

logger = logging.getLogger(__name__)


def get_db_session():
    """Get database session for tasks"""
    return SessionLocal()


@celery_app.task(bind=True, name='app.tasks.notification_tasks.send_threat_alert_task')
def send_threat_alert_task(self, threat_id: str, alert_type: str = "immediate"):
    """Send threat alert notification"""
    
    try:
        self.update_state(state='PROGRESS', meta={'progress': 0, 'status': 'Preparing threat alert'})
        
        db = get_db_session()
        
        try:
            # Get threat details
            threat = db.query(Threat).filter(Threat.id == UUID(threat_id)).first()
            if not threat:
                raise ValueError(f"Threat {threat_id} not found")
            
            # Get website and user details
            website = db.query(Website).filter(Website.id == threat.website_id).first()
            if not website:
                raise ValueError(f"Website for threat {threat_id} not found")
            
            user = db.query(User).filter(User.id == website.user_id).first()
            if not user:
                raise ValueError(f"User for website {website.id} not found")
            
            self.update_state(
                state='PROGRESS',
                meta={'progress': 30, 'status': 'Generating alert content'}
            )
            
            # Generate alert content
            alert_data = {
                'threat_id': threat_id,
                'threat_type': threat.threat_type,
                'severity': threat.severity,
                'confidence_score': threat.confidence_score,
                'website_name': website.name,
                'website_domain': website.domain,
                'source_ip': threat.source_ip,
                'source_country': threat.source_country,
                'detected_at': threat.detected_at.isoformat(),
                'attack_vector': threat.attack_vector,
                'description': threat.description,
                'user_email': user.email,
                'user_name': user.full_name
            }
            
            # Determine alert urgency
            urgency = "high" if threat.severity in ["critical", "high"] else "normal"
            
            self.update_state(
                state='PROGRESS',
                meta={'progress': 60, 'status': 'Sending notifications'}
            )
            
            # Send email notification (mock implementation)
            email_sent = await self._send_email_alert(alert_data, urgency)
            
            # Send SMS for critical threats (mock implementation)
            sms_sent = False
            if threat.severity == "critical":
                sms_sent = await self._send_sms_alert(alert_data)
            
            # Send webhook notification (mock implementation)
            webhook_sent = await self._send_webhook_alert(alert_data)
            
            # Log notification in database
            # In real implementation, would create notification record
            
            self.update_state(
                state='PROGRESS',
                meta={'progress': 100, 'status': 'Alert sent successfully'}
            )
            
            return {
                'status': 'completed',
                'threat_id': threat_id,
                'notifications_sent': {
                    'email': email_sent,
                    'sms': sms_sent,
                    'webhook': webhook_sent
                },
                'alert_type': alert_type,
                'urgency': urgency,
                'sent_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error sending threat alert: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@celery_app.task(bind=True, name='app.tasks.notification_tasks.send_scan_report_task')
def send_scan_report_task(self, scan_id: str, report_format: str = "html"):
    """Send scan completion report"""
    
    try:
        self.update_state(state='PROGRESS', meta={'progress': 0, 'status': 'Generating scan report'})
        
        db = get_db_session()
        
        try:
            from app.models.scan import Scan
            
            # Get scan details
            scan = db.query(Scan).filter(Scan.id == UUID(scan_id)).first()
            if not scan:
                raise ValueError(f"Scan {scan_id} not found")
            
            # Get website and user details
            website = db.query(Website).filter(Website.id == scan.website_id).first()
            user = db.query(User).filter(User.id == website.user_id).first()
            
            self.update_state(
                state='PROGRESS',
                meta={'progress': 40, 'status': 'Formatting report'}
            )
            
            # Generate report content
            report_data = {
                'scan_id': scan_id,
                'scan_type': scan.scan_type,
                'website_name': website.name,
                'website_domain': website.domain,
                'scan_started': scan.started_at.isoformat() if scan.started_at else None,
                'scan_completed': scan.completed_at.isoformat() if scan.completed_at else None,
                'scan_duration': int((scan.completed_at - scan.started_at).total_seconds()) if scan.started_at and scan.completed_at else 0,
                'scan_status': scan.status,
                'results': scan.results or {},
                'user_email': user.email,
                'user_name': user.full_name
            }
            
            # Format report based on type
            if report_format == "html":
                report_content = self._generate_html_report(report_data)
            elif report_format == "pdf":
                report_content = self._generate_pdf_report(report_data)
            else:
                report_content = self._generate_text_report(report_data)
            
            self.update_state(
                state='PROGRESS',
                meta={'progress': 80, 'status': 'Sending report'}
            )
            
            # Send report via email
            email_sent = await self._send_email_report(report_data, report_content, report_format)
            
            return {
                'status': 'completed',
                'scan_id': scan_id,
                'report_format': report_format,
                'email_sent': email_sent,
                'sent_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error sending scan report: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@celery_app.task(bind=True, name='app.tasks.notification_tasks.send_weekly_report_task')
def send_weekly_report_task(self):
    """Send weekly summary reports to all users"""
    
    try:
        self.update_state(state='PROGRESS', meta={'progress': 0, 'status': 'Generating weekly reports'})
        
        db = get_db_session()
        
        try:
            # Get all active users
            users = db.query(User).filter(User.is_active == True).all()
            
            reports_sent = 0
            total_users = len(users)
            
            for i, user in enumerate(users):
                progress = int((i + 1) / total_users * 100)
                
                self.update_state(
                    state='PROGRESS',
                    meta={'progress': progress, 'status': f'Generating report for {user.email}'}
                )
                
                # Get user's websites
                websites = db.query(Website).filter(Website.user_id == user.id).all()
                
                if not websites:
                    continue
                
                # Generate weekly summary
                end_date = datetime.utcnow()
                start_date = end_date - timedelta(days=7)
                
                summary_data = {
                    'user_name': user.full_name,
                    'user_email': user.email,
                    'report_period': {
                        'start': start_date.isoformat(),
                        'end': end_date.isoformat()
                    },
                    'websites': []
                }
                
                for website in websites:
                    # Get website statistics for the week
                    threats_count = db.query(Threat).filter(
                        and_(
                            Threat.website_id == website.id,
                            Threat.detected_at >= start_date
                        )
                    ).count()
                    
                    visitors_count = db.query(Visitor).filter(
                        and_(
                            Visitor.website_id == website.id,
                            Visitor.created_at >= start_date
                        )
                    ).count()
                    
                    website_summary = {
                        'name': website.name,
                        'domain': website.domain,
                        'threats_detected': threats_count,
                        'visitors': visitors_count,
                        'status': 'active' if website.is_active else 'inactive'
                    }
                    
                    summary_data['websites'].append(website_summary)
                
                # Send weekly report
                email_sent = await self._send_weekly_email_report(summary_data)
                
                if email_sent:
                    reports_sent += 1
            
            return {
                'status': 'completed',
                'total_users': total_users,
                'reports_sent': reports_sent,
                'sent_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error sending weekly reports: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


# Helper methods for sending notifications (mock implementations)

async def _send_email_alert(alert_data: Dict[str, Any], urgency: str) -> bool:
    """Send email alert (mock implementation)"""
    # In real implementation, would use email service like SendGrid, SES, etc.
    logger.info(f"Sending email alert for threat {alert_data['threat_id']} to {alert_data['user_email']}")
    return True


async def _send_sms_alert(alert_data: Dict[str, Any]) -> bool:
    """Send SMS alert (mock implementation)"""
    # In real implementation, would use SMS service like Twilio
    logger.info(f"Sending SMS alert for critical threat {alert_data['threat_id']}")
    return True


async def _send_webhook_alert(alert_data: Dict[str, Any]) -> bool:
    """Send webhook notification (mock implementation)"""
    # In real implementation, would send HTTP POST to configured webhook URL
    logger.info(f"Sending webhook alert for threat {alert_data['threat_id']}")
    return True


def _generate_html_report(report_data: Dict[str, Any]) -> str:
    """Generate HTML report (mock implementation)"""
    return f"""
    <html>
    <body>
        <h1>Scan Report - {report_data['website_name']}</h1>
        <p>Scan Type: {report_data['scan_type']}</p>
        <p>Status: {report_data['scan_status']}</p>
        <p>Duration: {report_data['scan_duration']} seconds</p>
        <!-- More report content would go here -->
    </body>
    </html>
    """


def _generate_pdf_report(report_data: Dict[str, Any]) -> bytes:
    """Generate PDF report (mock implementation)"""
    # In real implementation, would use library like ReportLab or WeasyPrint
    return b"Mock PDF content"


def _generate_text_report(report_data: Dict[str, Any]) -> str:
    """Generate text report"""
    return f"""
    Scan Report for {report_data['website_name']}
    ==========================================
    
    Scan Type: {report_data['scan_type']}
    Status: {report_data['scan_status']}
    Duration: {report_data['scan_duration']} seconds
    
    Results:
    {report_data['results']}
    """


async def _send_email_report(report_data: Dict[str, Any], content: str, format: str) -> bool:
    """Send email report (mock implementation)"""
    logger.info(f"Sending {format} report for scan {report_data['scan_id']} to {report_data['user_email']}")
    return True


async def _send_weekly_email_report(summary_data: Dict[str, Any]) -> bool:
    """Send weekly email report (mock implementation)"""
    logger.info(f"Sending weekly report to {summary_data['user_email']}")
    return True
