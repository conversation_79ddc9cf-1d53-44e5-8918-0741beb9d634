#!/usr/bin/env python3
"""
Simple test script to verify API endpoints are working
"""

import requests
import json
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

def test_endpoint(method: str, endpoint: str, data: Dict[Any, Any] = None, headers: Dict[str, str] = None) -> Dict[str, Any]:
    """Test an API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, headers=headers)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            return {"error": f"Unsupported method: {method}"}
        
        return {
            "status_code": response.status_code,
            "success": response.status_code < 400,
            "response": response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
        }
    except Exception as e:
        return {"error": str(e), "success": False}

def main():
    """Run API endpoint tests"""
    print("🧪 Testing ProSecurity Monitor Backend API Endpoints")
    print("=" * 60)
    
    # Test basic endpoints
    tests = [
        ("GET", "/", "Root endpoint"),
        ("GET", "/health", "Health check"),
        ("GET", "/docs", "API documentation"),
        ("GET", "/openapi.json", "OpenAPI schema"),
    ]
    
    # Test authentication endpoints
    auth_tests = [
        ("POST", "/api/v1/auth/register", "User registration", {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Test User"
        }),
        ("POST", "/api/v1/auth/login", "User login", {
            "username": "<EMAIL>",
            "password": "testpassword123"
        }),
    ]
    
    # Test protected endpoints (these will fail without auth, but should return proper error codes)
    protected_tests = [
        ("GET", "/api/v1/websites/", "List websites"),
        ("GET", "/api/v1/analytics/dashboard", "Dashboard analytics"),
    ]
    
    print("\n📋 Basic Endpoint Tests:")
    print("-" * 30)
    for method, endpoint, description in tests:
        result = test_endpoint(method, endpoint)
        status = "✅" if result.get("success") else "❌"
        print(f"{status} {description}: {result.get('status_code', 'ERROR')}")
        if not result.get("success") and "error" in result:
            print(f"   Error: {result['error']}")
    
    print("\n🔐 Authentication Endpoint Tests:")
    print("-" * 35)
    for method, endpoint, description, data in auth_tests:
        result = test_endpoint(method, endpoint, data)
        status = "✅" if result.get("success") else "❌"
        print(f"{status} {description}: {result.get('status_code', 'ERROR')}")
        if not result.get("success"):
            if "error" in result:
                print(f"   Error: {result['error']}")
            elif "response" in result:
                print(f"   Response: {result['response']}")
    
    print("\n🔒 Protected Endpoint Tests (Expected to fail without auth):")
    print("-" * 55)
    for method, endpoint, description in protected_tests:
        result = test_endpoint(method, endpoint)
        # For protected endpoints, we expect 401 Unauthorized
        expected_status = result.get("status_code") == 401
        status = "✅" if expected_status else "❌"
        print(f"{status} {description}: {result.get('status_code', 'ERROR')} {'(Expected 401)' if expected_status else ''}")
    
    print("\n📊 API Schema Validation:")
    print("-" * 25)
    
    # Test OpenAPI schema
    schema_result = test_endpoint("GET", "/openapi.json")
    if schema_result.get("success"):
        try:
            schema = schema_result["response"]
            endpoints_count = len(schema.get("paths", {}))
            components_count = len(schema.get("components", {}).get("schemas", {}))
            print(f"✅ OpenAPI Schema: {endpoints_count} endpoints, {components_count} schemas")
            
            # Count endpoints by tag
            endpoint_tags = {}
            for path, methods in schema.get("paths", {}).items():
                for method, details in methods.items():
                    if isinstance(details, dict) and "tags" in details:
                        for tag in details["tags"]:
                            endpoint_tags[tag] = endpoint_tags.get(tag, 0) + 1
            
            print("📈 Endpoints by category:")
            for tag, count in sorted(endpoint_tags.items()):
                print(f"   • {tag}: {count} endpoints")
                
        except Exception as e:
            print(f"❌ Schema validation error: {e}")
    else:
        print("❌ Failed to retrieve OpenAPI schema")
    
    print("\n" + "=" * 60)
    print("🎉 API Testing Complete!")
    print("\n💡 Next Steps:")
    print("   1. Set up database connection for full functionality")
    print("   2. Test with frontend integration")
    print("   3. Deploy to production environment")
    print("   4. Set up monitoring and logging")

if __name__ == "__main__":
    main()
