"""
Threat model for security threat detection and tracking
"""

from sqlalchemy import <PERSON>um<PERSON>, String, <PERSON>ole<PERSON>, DateTime, Integer, ForeignKey, Text, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB, INET
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum

from app.core.database import Base


class ThreatSeverity(str, enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ThreatType(str, enum.Enum):
    SQL_INJECTION = "sql_injection"
    XSS = "xss"
    CSRF = "csrf"
    BRUTE_FORCE = "brute_force"
    DDOS = "ddos"
    MALWARE = "malware"
    PHISHING = "phishing"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    DATA_BREACH = "data_breach"


class Threat(Base):
    __tablename__ = "threats"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    website_id = Column(UUID(as_uuid=True), ForeignKey("websites.id"), nullable=False)
    visitor_id = Column(UUID(as_uuid=True), ForeignKey("visitors.id"), nullable=True)
    
    # Threat classification
    threat_type = Column(Enum(ThreatType), nullable=False)
    severity = Column(Enum(ThreatSeverity), nullable=False)
    confidence_score = Column(Integer, default=0, nullable=False)  # 0-100
    
    # Threat details
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    attack_vector = Column(String(255), nullable=True)
    
    # Source information
    source_ip = Column(INET, nullable=True)
    source_country = Column(String(2), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # Request/Response data
    request_method = Column(String(10), nullable=True)
    request_url = Column(Text, nullable=True)
    request_headers = Column(JSONB, nullable=True)
    request_body = Column(Text, nullable=True)
    response_status = Column(Integer, nullable=True)
    
    # Detection information
    detection_method = Column(String(100), nullable=True)  # rule_based, ml_based, signature
    rule_id = Column(String(100), nullable=True)
    
    # Response and mitigation
    is_blocked = Column(Boolean, default=False, nullable=False)
    is_false_positive = Column(Boolean, default=False, nullable=False)
    mitigation_action = Column(String(100), nullable=True)
    
    # Additional data
    threat_metadata = Column(JSONB, nullable=True)
    evidence = Column(JSONB, nullable=True)
    
    # Timestamps
    detected_at = Column(DateTime(timezone=True), server_default=func.now())
    resolved_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    website = relationship("Website", back_populates="threats")
    visitor = relationship("Visitor", back_populates="threats")
    
    def __repr__(self):
        return f"<Threat(type='{self.threat_type}', severity='{self.severity}')>"
