"""
Advanced geolocation and IP intelligence service
"""

import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from ipaddress import ip_address, IPv4Address, IPv6Address
import json
from functools import lru_cache

from app.core.config import settings

logger = logging.getLogger(__name__)


class IPIntelligence:
    """IP intelligence data structure"""
    
    def __init__(self, data: Dict[str, Any]):
        self.ip = data.get('ip', '')
        self.country = data.get('country', '')
        self.country_code = data.get('country_code', '')
        self.region = data.get('region', '')
        self.region_code = data.get('region_code', '')
        self.city = data.get('city', '')
        self.postal_code = data.get('postal_code', '')
        self.latitude = data.get('latitude', 0.0)
        self.longitude = data.get('longitude', 0.0)
        self.timezone = data.get('timezone', '')
        
        # ISP and network information
        self.isp = data.get('isp', '')
        self.organization = data.get('organization', '')
        self.asn = data.get('asn', '')
        self.asn_name = data.get('asn_name', '')
        
        # Security flags
        self.is_tor = data.get('is_tor', False)
        self.is_vpn = data.get('is_vpn', False)
        self.is_proxy = data.get('is_proxy', False)
        self.is_hosting = data.get('is_hosting', False)
        self.is_datacenter = data.get('is_datacenter', False)
        self.is_mobile = data.get('is_mobile', False)
        
        # Risk assessment
        self.risk_score = data.get('risk_score', 0)
        self.threat_types = data.get('threat_types', [])
        self.reputation = data.get('reputation', 'unknown')
        
        # Additional metadata
        self.connection_type = data.get('connection_type', '')
        self.user_type = data.get('user_type', '')
        self.accuracy_radius = data.get('accuracy_radius', 0)
        
        # Timestamp
        self.updated_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'ip': self.ip,
            'country': self.country,
            'country_code': self.country_code,
            'region': self.region,
            'region_code': self.region_code,
            'city': self.city,
            'postal_code': self.postal_code,
            'latitude': self.latitude,
            'longitude': self.longitude,
            'timezone': self.timezone,
            'isp': self.isp,
            'organization': self.organization,
            'asn': self.asn,
            'asn_name': self.asn_name,
            'is_tor': self.is_tor,
            'is_vpn': self.is_vpn,
            'is_proxy': self.is_proxy,
            'is_hosting': self.is_hosting,
            'is_datacenter': self.is_datacenter,
            'is_mobile': self.is_mobile,
            'risk_score': self.risk_score,
            'threat_types': self.threat_types,
            'reputation': self.reputation,
            'connection_type': self.connection_type,
            'user_type': self.user_type,
            'accuracy_radius': self.accuracy_radius,
            'updated_at': self.updated_at.isoformat()
        }


class GeolocationService:
    """Advanced geolocation and IP intelligence service"""
    
    def __init__(self):
        self.cache = {}  # Simple in-memory cache
        self.cache_ttl = 3600  # 1 hour cache TTL
        
        # API endpoints and keys (would be configured via environment)
        self.apis = {
            'ipapi': {
                'url': 'http://ip-api.com/json/{ip}',
                'fields': 'status,message,country,countryCode,region,regionName,city,zip,lat,lon,timezone,isp,org,as,query',
                'rate_limit': 45  # requests per minute
            },
            'ipinfo': {
                'url': 'https://ipinfo.io/{ip}/json',
                'token': getattr(settings, 'IPINFO_TOKEN', None),
                'rate_limit': 50000  # requests per month
            },
            'maxmind': {
                'enabled': False,  # Would require MaxMind GeoIP2 database
                'database_path': 'data/GeoLite2-City.mmdb'
            }
        }
        
        # High-risk countries and regions
        self.high_risk_countries = {
            'CN', 'RU', 'KP', 'IR', 'PK', 'BD', 'VN', 'ID', 'NG', 'UA'
        }
        
        # Known VPN/Proxy providers
        self.vpn_providers = {
            'nordvpn', 'expressvpn', 'surfshark', 'cyberghost', 'purevpn',
            'hotspot shield', 'tunnelbear', 'windscribe', 'protonvpn'
        }
        
        # Tor exit node detection
        self.tor_exit_nodes = set()  # Would be populated from Tor directory
        
        # Request statistics for rate limiting
        self.request_stats = {}
    
    async def get_ip_intelligence(self, ip_str: str) -> Optional[IPIntelligence]:
        """Get comprehensive IP intelligence"""
        try:
            # Validate IP address
            ip_obj = ip_address(ip_str)
            
            # Check if private IP
            if ip_obj.is_private:
                return self._create_private_ip_intelligence(ip_str)
            
            # Check cache first
            cached_data = self._get_from_cache(ip_str)
            if cached_data:
                return IPIntelligence(cached_data)
            
            # Gather intelligence from multiple sources
            intelligence_data = await self._gather_intelligence(ip_str)
            
            # Cache the result
            self._cache_result(ip_str, intelligence_data)
            
            return IPIntelligence(intelligence_data)
            
        except Exception as e:
            logger.error(f"Error getting IP intelligence for {ip_str}: {e}")
            return None
    
    async def _gather_intelligence(self, ip_str: str) -> Dict[str, Any]:
        """Gather intelligence from multiple sources"""
        intelligence = {
            'ip': ip_str,
            'country': '',
            'country_code': '',
            'region': '',
            'city': '',
            'latitude': 0.0,
            'longitude': 0.0,
            'timezone': '',
            'isp': '',
            'organization': '',
            'asn': '',
            'is_tor': False,
            'is_vpn': False,
            'is_proxy': False,
            'is_hosting': False,
            'is_datacenter': False,
            'risk_score': 0,
            'threat_types': [],
            'reputation': 'unknown'
        }
        
        # Try multiple APIs for redundancy
        tasks = []
        
        # IP-API (free, good for basic geolocation)
        tasks.append(self._query_ipapi(ip_str))
        
        # IPInfo (good for ISP and organization data)
        if self.apis['ipinfo']['token']:
            tasks.append(self._query_ipinfo(ip_str))
        
        # Execute all queries concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Merge results from different sources
        for result in results:
            if isinstance(result, dict) and result:
                intelligence.update(result)
        
        # Enhance with additional analysis
        intelligence = await self._enhance_intelligence(intelligence)
        
        return intelligence
    
    async def _query_ipapi(self, ip_str: str) -> Dict[str, Any]:
        """Query IP-API service"""
        try:
            url = self.apis['ipapi']['url'].format(ip=ip_str)
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if data.get('status') == 'success':
                            return {
                                'country': data.get('country', ''),
                                'country_code': data.get('countryCode', ''),
                                'region': data.get('regionName', ''),
                                'region_code': data.get('region', ''),
                                'city': data.get('city', ''),
                                'postal_code': data.get('zip', ''),
                                'latitude': float(data.get('lat', 0)),
                                'longitude': float(data.get('lon', 0)),
                                'timezone': data.get('timezone', ''),
                                'isp': data.get('isp', ''),
                                'organization': data.get('org', ''),
                                'asn': data.get('as', '').split()[0] if data.get('as') else ''
                            }
        except Exception as e:
            logger.error(f"Error querying IP-API for {ip_str}: {e}")
        
        return {}
    
    async def _query_ipinfo(self, ip_str: str) -> Dict[str, Any]:
        """Query IPInfo service"""
        try:
            url = self.apis['ipinfo']['url'].format(ip=ip_str)
            headers = {}
            
            if self.apis['ipinfo']['token']:
                headers['Authorization'] = f"Bearer {self.apis['ipinfo']['token']}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Parse location
                        loc = data.get('loc', '').split(',')
                        lat, lon = (float(loc[0]), float(loc[1])) if len(loc) == 2 else (0.0, 0.0)
                        
                        return {
                            'country': data.get('country', ''),
                            'region': data.get('region', ''),
                            'city': data.get('city', ''),
                            'postal_code': data.get('postal', ''),
                            'latitude': lat,
                            'longitude': lon,
                            'timezone': data.get('timezone', ''),
                            'organization': data.get('org', ''),
                            'is_hosting': 'hosting' in data.get('org', '').lower(),
                            'is_datacenter': any(term in data.get('org', '').lower() 
                                               for term in ['datacenter', 'cloud', 'aws', 'azure', 'google'])
                        }
        except Exception as e:
            logger.error(f"Error querying IPInfo for {ip_str}: {e}")
        
        return {}
    
    async def _enhance_intelligence(self, intelligence: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance intelligence with additional analysis"""
        ip_str = intelligence['ip']
        
        # Check if Tor exit node
        intelligence['is_tor'] = await self._is_tor_exit_node(ip_str)
        
        # Check if VPN/Proxy
        if not intelligence['is_vpn']:
            intelligence['is_vpn'] = await self._is_vpn_provider(intelligence)
        
        if not intelligence['is_proxy']:
            intelligence['is_proxy'] = await self._is_proxy_service(intelligence)
        
        # Calculate risk score
        intelligence['risk_score'] = self._calculate_risk_score(intelligence)
        
        # Determine threat types
        intelligence['threat_types'] = self._identify_threat_types(intelligence)
        
        # Assess reputation
        intelligence['reputation'] = self._assess_reputation(intelligence)
        
        return intelligence
    
    async def _is_tor_exit_node(self, ip_str: str) -> bool:
        """Check if IP is a Tor exit node"""
        # In a real implementation, this would check against Tor directory
        # For now, return False as placeholder
        return False
    
    async def _is_vpn_provider(self, intelligence: Dict[str, Any]) -> bool:
        """Check if IP belongs to a VPN provider"""
        org = intelligence.get('organization', '').lower()
        isp = intelligence.get('isp', '').lower()
        
        # Check against known VPN providers
        for provider in self.vpn_providers:
            if provider in org or provider in isp:
                return True
        
        # Check for VPN-related keywords
        vpn_keywords = ['vpn', 'virtual private', 'proxy', 'anonymous']
        for keyword in vpn_keywords:
            if keyword in org or keyword in isp:
                return True
        
        return False
    
    async def _is_proxy_service(self, intelligence: Dict[str, Any]) -> bool:
        """Check if IP belongs to a proxy service"""
        org = intelligence.get('organization', '').lower()
        
        proxy_keywords = ['proxy', 'socks', 'http proxy', 'web proxy']
        return any(keyword in org for keyword in proxy_keywords)
    
    def _calculate_risk_score(self, intelligence: Dict[str, Any]) -> int:
        """Calculate risk score (0-100)"""
        score = 0
        
        # Geographic risk
        if intelligence.get('country_code') in self.high_risk_countries:
            score += 30
        
        # Anonymization services
        if intelligence.get('is_tor'):
            score += 40
        if intelligence.get('is_vpn'):
            score += 25
        if intelligence.get('is_proxy'):
            score += 20
        
        # Hosting/datacenter
        if intelligence.get('is_hosting'):
            score += 15
        if intelligence.get('is_datacenter'):
            score += 10
        
        # ISP reputation (simplified)
        org = intelligence.get('organization', '').lower()
        if any(term in org for term in ['bulletproof', 'offshore', 'anonymous']):
            score += 35
        
        return min(100, score)
    
    def _identify_threat_types(self, intelligence: Dict[str, Any]) -> List[str]:
        """Identify potential threat types based on intelligence"""
        threats = []
        
        if intelligence.get('is_tor'):
            threats.extend(['anonymization', 'illegal_activity'])
        
        if intelligence.get('is_vpn') or intelligence.get('is_proxy'):
            threats.extend(['anonymization', 'geo_evasion'])
        
        if intelligence.get('is_hosting') or intelligence.get('is_datacenter'):
            threats.extend(['automated_attacks', 'bot_activity'])
        
        if intelligence.get('country_code') in self.high_risk_countries:
            threats.extend(['state_sponsored', 'cybercrime'])
        
        if intelligence.get('risk_score', 0) > 70:
            threats.append('high_risk')
        
        return list(set(threats))  # Remove duplicates
    
    def _assess_reputation(self, intelligence: Dict[str, Any]) -> str:
        """Assess IP reputation"""
        risk_score = intelligence.get('risk_score', 0)
        
        if risk_score >= 80:
            return 'malicious'
        elif risk_score >= 60:
            return 'suspicious'
        elif risk_score >= 30:
            return 'questionable'
        else:
            return 'clean'
    
    def _create_private_ip_intelligence(self, ip_str: str) -> IPIntelligence:
        """Create intelligence for private IP addresses"""
        return IPIntelligence({
            'ip': ip_str,
            'country': 'Private Network',
            'country_code': 'XX',
            'region': 'Private',
            'city': 'Private',
            'latitude': 0.0,
            'longitude': 0.0,
            'timezone': '',
            'isp': 'Private Network',
            'organization': 'Private Network',
            'asn': '',
            'is_tor': False,
            'is_vpn': False,
            'is_proxy': False,
            'is_hosting': False,
            'is_datacenter': False,
            'risk_score': 0,
            'threat_types': [],
            'reputation': 'private'
        })
    
    def _get_from_cache(self, ip_str: str) -> Optional[Dict[str, Any]]:
        """Get data from cache if not expired"""
        if ip_str in self.cache:
            cached_data, timestamp = self.cache[ip_str]
            if datetime.utcnow() - timestamp < timedelta(seconds=self.cache_ttl):
                return cached_data
            else:
                del self.cache[ip_str]
        return None
    
    def _cache_result(self, ip_str: str, data: Dict[str, Any]):
        """Cache the result"""
        self.cache[ip_str] = (data, datetime.utcnow())
        
        # Simple cache cleanup (remove old entries)
        if len(self.cache) > 10000:  # Max cache size
            # Remove oldest 20% of entries
            sorted_items = sorted(self.cache.items(), key=lambda x: x[1][1])
            for ip, _ in sorted_items[:2000]:
                del self.cache[ip]
    
    @lru_cache(maxsize=1000)
    def get_country_risk_level(self, country_code: str) -> str:
        """Get risk level for a country"""
        if country_code in self.high_risk_countries:
            return 'high'
        elif country_code in {'US', 'CA', 'GB', 'DE', 'FR', 'AU', 'JP', 'KR'}:
            return 'low'
        else:
            return 'medium'
    
    async def bulk_lookup(self, ip_list: List[str]) -> Dict[str, Optional[IPIntelligence]]:
        """Perform bulk IP lookups"""
        tasks = [self.get_ip_intelligence(ip) for ip in ip_list]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return {
            ip: result if not isinstance(result, Exception) else None
            for ip, result in zip(ip_list, results)
        }


# Global geolocation service instance
geolocation_service = GeolocationService()
