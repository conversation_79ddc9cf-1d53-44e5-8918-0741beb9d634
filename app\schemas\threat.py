"""
Threat schemas for request/response models
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field, IPvAnyAddress
from enum import Enum


class ThreatSeverity(str, Enum):
    """Threat severity enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ThreatType(str, Enum):
    """Threat type enumeration"""
    SQL_INJECTION = "sql_injection"
    XSS = "xss"
    CSRF = "csrf"
    BRUTE_FORCE = "brute_force"
    DDOS = "ddos"
    MALWARE = "malware"
    PHISHING = "phishing"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    DATA_BREACH = "data_breach"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    INVALID_INPUT = "invalid_input"


class DetectionMethod(str, Enum):
    """Detection method enumeration"""
    RULE_BASED = "rule_based"
    ML_BASED = "ml_based"
    SIGNATURE = "signature"
    BEHAVIORAL = "behavioral"
    HEURISTIC = "heuristic"


class ThreatCreate(BaseModel):
    """Schema for creating a new threat record"""
    website_id: UUID
    visitor_id: Optional[UUID] = None
    
    # Threat classification
    threat_type: ThreatType
    severity: ThreatSeverity
    confidence_score: int = Field(..., ge=0, le=100, description="Confidence score 0-100")
    
    # Threat details
    title: str = Field(..., max_length=255)
    description: Optional[str] = Field(None, max_length=2000)
    attack_vector: Optional[str] = Field(None, max_length=255)
    
    # Source information
    source_ip: Optional[IPvAnyAddress] = None
    source_country: Optional[str] = Field(None, max_length=2)
    user_agent: Optional[str] = Field(None, max_length=1000)
    
    # Request/Response data
    request_method: Optional[str] = Field(None, max_length=10)
    request_url: Optional[str] = Field(None, max_length=2000)
    request_headers: Optional[Dict[str, Any]] = None
    request_body: Optional[str] = Field(None, max_length=10000)
    response_status: Optional[int] = Field(None, ge=100, le=599)
    
    # Detection information
    detection_method: DetectionMethod = DetectionMethod.RULE_BASED
    rule_id: Optional[str] = Field(None, max_length=100)
    
    # Additional data
    threat_metadata: Optional[Dict[str, Any]] = None
    evidence: Optional[Dict[str, Any]] = None


class ThreatUpdate(BaseModel):
    """Schema for updating threat information"""
    severity: Optional[ThreatSeverity] = None
    confidence_score: Optional[int] = Field(None, ge=0, le=100)
    description: Optional[str] = Field(None, max_length=2000)
    is_blocked: Optional[bool] = None
    is_false_positive: Optional[bool] = None
    mitigation_action: Optional[str] = Field(None, max_length=100)
    resolved_at: Optional[datetime] = None
    threat_metadata: Optional[Dict[str, Any]] = None


class ThreatResponse(BaseModel):
    """Schema for threat response"""
    id: UUID
    website_id: UUID
    visitor_id: Optional[UUID]
    
    # Threat classification
    threat_type: str
    severity: str
    confidence_score: int
    
    # Threat details
    title: str
    description: Optional[str]
    attack_vector: Optional[str]
    
    # Source information
    source_ip: Optional[str]
    source_country: Optional[str]
    user_agent: Optional[str]
    
    # Request/Response data
    request_method: Optional[str]
    request_url: Optional[str]
    request_headers: Optional[Dict[str, Any]]
    request_body: Optional[str]
    response_status: Optional[int]
    
    # Detection information
    detection_method: str
    rule_id: Optional[str]
    
    # Response and mitigation
    is_blocked: bool
    is_false_positive: bool
    mitigation_action: Optional[str]
    
    # Additional data
    threat_metadata: Optional[Dict[str, Any]]
    evidence: Optional[Dict[str, Any]]
    
    # Timestamps
    detected_at: datetime
    resolved_at: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class ThreatDetailResponse(ThreatResponse):
    """Detailed threat response with additional context"""
    visitor_info: Optional[Dict[str, Any]] = None
    related_threats: List[UUID] = []
    impact_assessment: Optional[Dict[str, Any]] = None
    remediation_steps: List[str] = []


class ThreatListResponse(BaseModel):
    """Response for threat listing with pagination"""
    threats: List[ThreatResponse]
    total: int
    page: int
    per_page: int
    pages: int


class ThreatAnalytics(BaseModel):
    """Threat analytics data"""
    total_threats: int
    threats_by_severity: Dict[str, int]
    threats_by_type: Dict[str, int]
    threats_by_country: List[Dict[str, Any]]
    blocked_threats: int
    false_positives: int
    avg_confidence_score: float
    threat_trend: List[Dict[str, Any]]
    top_attack_vectors: List[Dict[str, Any]]


class ThreatRealTimeData(BaseModel):
    """Real-time threat data"""
    active_threats: int
    recent_threats: List[ThreatResponse]
    threat_map: List[Dict[str, Any]]
    severity_breakdown: Dict[str, int]
    blocked_ips: List[str]
    timestamp: datetime


class ThreatFilter(BaseModel):
    """Threat filtering options"""
    threat_type: Optional[ThreatType] = None
    severity: Optional[ThreatSeverity] = None
    detection_method: Optional[DetectionMethod] = None
    source_country: Optional[str] = None
    is_blocked: Optional[bool] = None
    is_false_positive: Optional[bool] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    min_confidence: Optional[int] = Field(None, ge=0, le=100)
    max_confidence: Optional[int] = Field(None, ge=0, le=100)


class ThreatBulkAction(BaseModel):
    """Bulk action on threats"""
    threat_ids: List[UUID]
    action: str = Field(..., description="Action: block, resolve, mark_false_positive, delete")
    parameters: Optional[Dict[str, Any]] = None


class ThreatRule(BaseModel):
    """Threat detection rule"""
    id: Optional[UUID] = None
    name: str = Field(..., max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    threat_type: ThreatType
    severity: ThreatSeverity
    pattern: str = Field(..., description="Detection pattern or regex")
    is_active: bool = True
    confidence_score: int = Field(..., ge=0, le=100)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class ThreatRuleResponse(ThreatRule):
    """Threat rule response"""
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    matches_count: int = 0
    last_match: Optional[datetime] = None

    class Config:
        from_attributes = True
