"""
Blocking service for business logic
"""

import ipaddress
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException, status

from app.models.blocking_rule import BlockingRule
from app.models.website import Website
from app.schemas.blocking_rule import (
    BlockingRuleCreate, BlockingRuleUpdate, BlockingRuleResponse,
    BlockingRuleDetailResponse, BlockingRuleListResponse, BlockingRuleFilter,
    BlockingRuleBulkAction, BlockingRuleStats, BlockingRuleMatch
)
from app.services.base_service import BaseService


class BlockingService(BaseService[BlockingRule, BlockingRuleCreate, BlockingRuleUpdate]):
    """Blocking service with business logic"""
    
    def __init__(self):
        super().__init__(BlockingRule)
    
    async def create_blocking_rule(
        self,
        db: Session,
        *,
        rule_data: BlockingRuleCreate,
        user_id: UUID
    ) -> BlockingRule:
        """Create a new blocking rule"""
        # If website_id is provided, verify ownership
        if rule_data.website_id:
            website = db.query(Website).filter(
                and_(Website.id == rule_data.website_id, Website.user_id == user_id)
            ).first()
            
            if not website:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Website not found or access denied"
                )
        
        # Check for duplicate rules
        existing_rule = await self._check_duplicate_rule(db, rule_data, user_id)
        if existing_rule:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="A similar blocking rule already exists"
            )
        
        # Validate rule value based on type
        await self._validate_rule_value(rule_data.rule_type, rule_data.rule_value)
        
        # Create rule
        return await self.create(db, obj_in=rule_data)
    
    async def get_user_blocking_rules(
        self,
        db: Session,
        *,
        user_id: UUID,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[BlockingRuleFilter] = None
    ) -> BlockingRuleListResponse:
        """Get blocking rules for a user"""
        # Build query - include global rules and user's website rules
        query = db.query(BlockingRule).outerjoin(Website).filter(
            or_(
                BlockingRule.website_id.is_(None),  # Global rules
                Website.user_id == user_id  # User's website rules
            )
        )
        
        if filters:
            query = self._apply_blocking_rule_filters(query, filters)
        
        # Get total count
        total = query.count()
        
        # Get paginated results
        rules = query.order_by(
            BlockingRule.priority.asc(),
            desc(BlockingRule.created_at)
        ).offset(skip).limit(limit).all()
        
        pages = (total + limit - 1) // limit
        
        return BlockingRuleListResponse(
            rules=[BlockingRuleResponse.from_orm(r) for r in rules],
            total=total,
            page=(skip // limit) + 1,
            per_page=limit,
            pages=pages
        )
    
    async def get_blocking_rule_detail(
        self,
        db: Session,
        *,
        rule_id: UUID,
        user_id: UUID
    ) -> BlockingRuleDetailResponse:
        """Get detailed blocking rule information"""
        rule = await self.get_or_404(db, rule_id)
        
        # Verify access
        if rule.website_id:
            website = db.query(Website).filter(
                and_(Website.id == rule.website_id, Website.user_id == user_id)
            ).first()
            
            if not website:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
        
        # Get additional rule details
        detail_data = BlockingRuleResponse.from_orm(rule).dict()
        
        # Add website info if applicable
        if rule.website_id:
            website = db.query(Website).filter(Website.id == rule.website_id).first()
            if website:
                detail_data["website_info"] = {
                    "domain": website.domain,
                    "name": website.name
                }
        
        # Calculate effectiveness metrics (simplified)
        detail_data["effectiveness_score"] = 85.0
        detail_data["false_positive_rate"] = 5.0
        detail_data["recent_matches"] = []  # TODO: Implement match tracking
        
        return BlockingRuleDetailResponse(**detail_data)
    
    async def update_blocking_rule(
        self,
        db: Session,
        *,
        rule_id: UUID,
        rule_data: BlockingRuleUpdate,
        user_id: UUID
    ) -> BlockingRule:
        """Update a blocking rule"""
        rule = await self.get_or_404(db, rule_id)
        
        # Verify access
        if rule.website_id:
            website = db.query(Website).filter(
                and_(Website.id == rule.website_id, Website.user_id == user_id)
            ).first()
            
            if not website:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
        
        return await self.update(db, db_obj=rule, obj_in=rule_data)
    
    async def delete_blocking_rule(
        self,
        db: Session,
        *,
        rule_id: UUID,
        user_id: UUID
    ) -> BlockingRule:
        """Delete a blocking rule"""
        rule = await self.get_or_404(db, rule_id)
        
        # Verify access
        if rule.website_id:
            website = db.query(Website).filter(
                and_(Website.id == rule.website_id, Website.user_id == user_id)
            ).first()
            
            if not website:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
        
        return await self.delete(db, id=rule_id)
    
    async def check_blocking_rules(
        self,
        db: Session,
        *,
        website_id: UUID,
        request_data: Dict[str, Any]
    ) -> List[BlockingRule]:
        """Check if request should be blocked by any rules"""
        # Get applicable rules for the website
        rules = db.query(BlockingRule).filter(
            and_(
                or_(
                    BlockingRule.website_id == website_id,
                    BlockingRule.website_id.is_(None)  # Global rules
                ),
                BlockingRule.is_active == True,
                or_(
                    BlockingRule.expires_at.is_(None),
                    BlockingRule.expires_at > datetime.utcnow()
                )
            )
        ).order_by(BlockingRule.priority.asc()).all()
        
        matching_rules = []
        
        for rule in rules:
            if await self._check_rule_match(rule, request_data):
                matching_rules.append(rule)
                
                # Update rule statistics
                rule.matches_count += 1
                rule.last_match = datetime.utcnow()
                
                # If it's a blocking rule, we can stop here
                if rule.action == "block":
                    break
        
        db.commit()
        return matching_rules
    
    async def get_blocking_stats(
        self,
        db: Session,
        *,
        user_id: UUID,
        website_id: Optional[UUID] = None
    ) -> BlockingRuleStats:
        """Get blocking rule statistics"""
        # Build base query
        if website_id:
            # Verify website ownership
            website = db.query(Website).filter(
                and_(Website.id == website_id, Website.user_id == user_id)
            ).first()
            
            if not website:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Website not found or access denied"
                )
            
            base_query = db.query(BlockingRule).filter(
                or_(
                    BlockingRule.website_id == website_id,
                    BlockingRule.website_id.is_(None)
                )
            )
        else:
            base_query = db.query(BlockingRule).outerjoin(Website).filter(
                or_(
                    BlockingRule.website_id.is_(None),
                    Website.user_id == user_id
                )
            )
        
        # Basic counts
        total_rules = base_query.count()
        active_rules = base_query.filter(BlockingRule.is_active == True).count()
        expired_rules = base_query.filter(
            and_(
                BlockingRule.expires_at.isnot(None),
                BlockingRule.expires_at <= datetime.utcnow()
            )
        ).count()
        
        # Rules by type
        type_counts = db.query(
            BlockingRule.rule_type,
            func.count(BlockingRule.id).label('count')
        ).filter(base_query.whereclause).group_by(BlockingRule.rule_type).all()
        
        rules_by_type = {t.rule_type: t.count for t in type_counts}
        
        # Rules by action
        action_counts = db.query(
            BlockingRule.action,
            func.count(BlockingRule.id).label('count')
        ).filter(base_query.whereclause).group_by(BlockingRule.action).all()
        
        rules_by_action = {a.action: a.count for a in action_counts}
        
        # Match statistics
        total_matches = db.query(func.sum(BlockingRule.matches_count)).filter(
            base_query.whereclause
        ).scalar() or 0
        
        blocked_requests = db.query(func.sum(BlockingRule.matches_count)).filter(
            and_(
                base_query.whereclause,
                BlockingRule.action == "block"
            )
        ).scalar() or 0
        
        allowed_requests = db.query(func.sum(BlockingRule.matches_count)).filter(
            and_(
                base_query.whereclause,
                BlockingRule.action == "allow"
            )
        ).scalar() or 0
        
        # Top matching rules
        top_rules = base_query.filter(
            BlockingRule.matches_count > 0
        ).order_by(desc(BlockingRule.matches_count)).limit(5).all()
        
        top_matching_rules = [
            {
                "rule_id": str(rule.id),
                "name": rule.name or f"{rule.rule_type}: {rule.rule_value}",
                "matches": rule.matches_count,
                "action": rule.action
            }
            for rule in top_rules
        ]
        
        return BlockingRuleStats(
            total_rules=total_rules,
            active_rules=active_rules,
            expired_rules=expired_rules,
            rules_by_type=rules_by_type,
            rules_by_action=rules_by_action,
            total_matches=total_matches,
            blocked_requests=blocked_requests,
            allowed_requests=allowed_requests,
            top_matching_rules=top_matching_rules
        )
    
    async def bulk_action_rules(
        self,
        db: Session,
        *,
        bulk_action: BlockingRuleBulkAction,
        user_id: UUID
    ) -> Dict[str, Any]:
        """Perform bulk action on blocking rules"""
        # Verify all rules belong to user
        rules = db.query(BlockingRule).outerjoin(Website).filter(
            and_(
                BlockingRule.id.in_(bulk_action.rule_ids),
                or_(
                    BlockingRule.website_id.is_(None),
                    Website.user_id == user_id
                )
            )
        ).all()
        
        if len(rules) != len(bulk_action.rule_ids):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Some rules not found or access denied"
            )
        
        # Perform action
        updated_count = 0
        if bulk_action.action == "activate":
            for rule in rules:
                rule.is_active = True
                updated_count += 1
        elif bulk_action.action == "deactivate":
            for rule in rules:
                rule.is_active = False
                updated_count += 1
        elif bulk_action.action == "update_priority":
            priority = bulk_action.parameters.get("priority") if bulk_action.parameters else None
            if priority:
                for rule in rules:
                    rule.priority = priority
                    updated_count += 1
        
        db.commit()
        
        return {
            "action": bulk_action.action,
            "updated_count": updated_count,
            "total_requested": len(bulk_action.rule_ids)
        }
    
    async def _check_duplicate_rule(
        self,
        db: Session,
        rule_data: BlockingRuleCreate,
        user_id: UUID
    ) -> Optional[BlockingRule]:
        """Check for duplicate blocking rules"""
        query = db.query(BlockingRule)
        
        if rule_data.website_id:
            query = query.filter(BlockingRule.website_id == rule_data.website_id)
        else:
            query = query.filter(BlockingRule.website_id.is_(None))
        
        return query.filter(
            and_(
                BlockingRule.rule_type == rule_data.rule_type,
                BlockingRule.rule_value == rule_data.rule_value,
                BlockingRule.action == rule_data.action
            )
        ).first()
    
    async def _validate_rule_value(self, rule_type: str, rule_value: str):
        """Validate rule value based on rule type"""
        if rule_type == "ip":
            try:
                ipaddress.ip_address(rule_value)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid IP address format"
                )
        elif rule_type == "ip_range":
            try:
                ipaddress.ip_network(rule_value, strict=False)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid IP range format (use CIDR notation)"
                )
        elif rule_type == "country":
            if len(rule_value) != 2 or not rule_value.isalpha():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Country code must be 2-letter ISO code"
                )
    
    async def _check_rule_match(
        self,
        rule: BlockingRule,
        request_data: Dict[str, Any]
    ) -> bool:
        """Check if a rule matches the request data"""
        if rule.rule_type == "ip":
            return request_data.get("ip") == rule.rule_value
        elif rule.rule_type == "ip_range":
            try:
                ip = ipaddress.ip_address(request_data.get("ip", ""))
                network = ipaddress.ip_network(rule.rule_value, strict=False)
                return ip in network
            except ValueError:
                return False
        elif rule.rule_type == "country":
            return request_data.get("country") == rule.rule_value
        elif rule.rule_type == "user_agent":
            user_agent = request_data.get("user_agent", "")
            return rule.rule_value.lower() in user_agent.lower()
        elif rule.rule_type == "device_type":
            return request_data.get("device_type") == rule.rule_value
        elif rule.rule_type == "threat_score":
            try:
                threshold = int(rule.rule_value)
                threat_score = request_data.get("threat_score", 0)
                return threat_score >= threshold
            except ValueError:
                return False
        
        return False
    
    def _apply_blocking_rule_filters(self, query, filters: BlockingRuleFilter):
        """Apply filters to blocking rule query"""
        if filters.website_id:
            query = query.filter(BlockingRule.website_id == filters.website_id)
        if filters.rule_type:
            query = query.filter(BlockingRule.rule_type == filters.rule_type)
        if filters.action:
            query = query.filter(BlockingRule.action == filters.action)
        if filters.scope:
            query = query.filter(BlockingRule.scope == filters.scope)
        if filters.is_active is not None:
            query = query.filter(BlockingRule.is_active == filters.is_active)
        if filters.is_expired is not None:
            if filters.is_expired:
                query = query.filter(
                    and_(
                        BlockingRule.expires_at.isnot(None),
                        BlockingRule.expires_at <= datetime.utcnow()
                    )
                )
            else:
                query = query.filter(
                    or_(
                        BlockingRule.expires_at.is_(None),
                        BlockingRule.expires_at > datetime.utcnow()
                    )
                )
        if filters.tags:
            # This would require proper JSON querying for tags
            pass
        if filters.date_from:
            query = query.filter(BlockingRule.created_at >= filters.date_from)
        if filters.date_to:
            query = query.filter(BlockingRule.created_at <= filters.date_to)
        
        return query
