"""
Blocking system service
"""

import asyncio
import logging
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Dict, Any, <PERSON><PERSON>
from uuid import UUID
import ipaddress
import re

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc

from app.services.base_service import BaseService
from app.services.event_service import event_service
from app.services.geolocation_service import geolocation_service
from app.models.blocking import (
    SecurityRule, WhitelistEntry, BlockingEvent, RateLimitTracker,
    BlockingRuleType, BlockingAction
)
from app.models.website import Website
from app.schemas.blocking import (
    BlockingRuleCreate, BlockingRuleUpdate, BlockingRuleResponse,
    WhitelistEntryCreate, WhitelistEntryUpdate, WhitelistEntryResponse,
    IPBlockRequest, CountryBlockRequest, BulkBlockRequest,
    BlockingAnalytics, RateLimitStatus
)

logger = logging.getLogger(__name__)


class BlockingService(BaseService[SecurityRule, BlockingRuleCreate, BlockingRuleUpdate]):
    """Service for managing blocking rules and enforcement"""

    def __init__(self):
        super().__init__(SecurityRule)

    async def create_blocking_rule(
        self,
        db: Session,
        *,
        rule_data: BlockingRuleCreate,
        user_id: UUID
    ) -> SecurityRule:
        """Create a new blocking rule"""
        try:
            # Validate rule value based on type
            await self._validate_rule_value(rule_data.rule_type, rule_data.rule_value)

            # Create rule
            rule_dict = rule_data.dict()
            rule_dict['user_id'] = user_id
            rule_dict['created_by'] = f"user_{user_id}"

            rule = await self.create(db, obj_in=rule_dict)

            # Emit event
            asyncio.create_task(event_service.emit_system_event(
                event_type="blocking_rule_created",
                message=f"Blocking rule '{rule.name}' created",
                website_id=rule.website_id,
                component="blocking_system",
                details={
                    "rule_id": str(rule.id),
                    "rule_type": rule.rule_type,
                    "action": rule.action
                }
            ))

            return rule

        except Exception as e:
            logger.error(f"Error creating blocking rule: {e}")
            raise
    
    async def get_user_blocking_rules(
        self,
        db: Session,
        *,
        user_id: UUID,
        website_id: Optional[UUID] = None,
        rule_type: Optional[BlockingRuleType] = None,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Dict[str, Any]:
        """Get blocking rules for a user with filtering"""
        query = db.query(SecurityRule).filter(SecurityRule.user_id == user_id)

        if website_id:
            query = query.filter(SecurityRule.website_id == website_id)
        if rule_type:
            query = query.filter(SecurityRule.rule_type == rule_type)
        if is_active is not None:
            query = query.filter(SecurityRule.is_active == is_active)

        # Get total count
        total = query.count()

        # Apply pagination and ordering
        rules = query.order_by(desc(SecurityRule.priority), desc(SecurityRule.created_at))\
                    .offset(skip).limit(limit).all()

        return {
            "rules": rules,
            "total": total,
            "page": (skip // limit) + 1,
            "per_page": limit,
            "has_next": skip + limit < total,
            "has_prev": skip > 0
        }
    
    async def update_blocking_rule(
        self,
        db: Session,
        *,
        rule_id: UUID,
        rule_data: BlockingRuleUpdate,
        user_id: UUID
    ) -> SecurityRule:
        """Update a blocking rule"""
        rule = await self.get(db, id=rule_id)
        if not rule or rule.user_id != user_id:
            raise ValueError("Blocking rule not found")

        # Validate rule value if being updated
        if rule_data.rule_value:
            await self._validate_rule_value(rule.rule_type, rule_data.rule_value)

        updated_rule = await self.update(db, db_obj=rule, obj_in=rule_data)

        # Emit event
        asyncio.create_task(event_service.emit_system_event(
            event_type="blocking_rule_updated",
            message=f"Blocking rule '{updated_rule.name}' updated",
            website_id=updated_rule.website_id,
            component="blocking_system",
            details={"rule_id": str(rule_id)}
        ))

        return updated_rule
    
    async def delete_blocking_rule(
        self,
        db: Session,
        *,
        rule_id: UUID,
        user_id: UUID
    ) -> bool:
        """Delete a blocking rule"""
        rule = await self.get(db, id=rule_id)
        if not rule or rule.user_id != user_id:
            raise ValueError("Blocking rule not found")

        await self.delete(db, id=rule_id)

        # Emit event
        asyncio.create_task(event_service.emit_system_event(
            event_type="blocking_rule_deleted",
            message=f"Blocking rule '{rule.name}' deleted",
            website_id=rule.website_id,
            component="blocking_system",
            details={"rule_id": str(rule_id)}
        ))

        return True
    
    async def block_ip_address(
        self,
        db: Session,
        *,
        request_data: IPBlockRequest,
        user_id: UUID
    ) -> SecurityRule:
        """Block a specific IP address"""
        rule_data = BlockingRuleCreate(
            website_id=request_data.website_id,
            name=f"IP Block: {request_data.ip_address}",
            description=request_data.reason or f"Blocked IP {request_data.ip_address}",
            rule_type=BlockingRuleType.IP_ADDRESS,
            action=BlockingAction.BLOCK,
            rule_value=request_data.ip_address,
            is_temporary=request_data.is_temporary,
            expires_at=request_data.expires_at,
            priority=50  # High priority for IP blocks
        )

        return await self.create_blocking_rule(db, rule_data=rule_data, user_id=user_id)
    
    async def block_country(
        self,
        db: Session,
        *,
        request_data: CountryBlockRequest,
        user_id: UUID
    ) -> SecurityRule:
        """Block a specific country"""
        rule_data = BlockingRuleCreate(
            website_id=request_data.website_id,
            name=f"Country Block: {request_data.country_code}",
            description=request_data.reason or f"Blocked country {request_data.country_code}",
            rule_type=BlockingRuleType.COUNTRY,
            action=BlockingAction.BLOCK,
            rule_value=request_data.country_code,
            is_temporary=request_data.is_temporary,
            expires_at=request_data.expires_at,
            priority=75  # Medium priority for country blocks
        )

        return await self.create_blocking_rule(db, rule_data=rule_data, user_id=user_id)

    async def bulk_block(
        self,
        db: Session,
        *,
        request_data: BulkBlockRequest,
        user_id: UUID
    ) -> List[SecurityRule]:
        """Create multiple blocking rules"""
        rules = []

        for value in request_data.values:
            try:
                # Validate value based on type
                await self._validate_rule_value(request_data.rule_type, value)

                rule_data = BlockingRuleCreate(
                    website_id=request_data.website_id,
                    name=f"Bulk {request_data.rule_type.value}: {value}",
                    description=request_data.reason or f"Bulk blocked {value}",
                    rule_type=request_data.rule_type,
                    action=BlockingAction.BLOCK,
                    rule_value=value,
                    is_temporary=request_data.is_temporary,
                    expires_at=request_data.expires_at,
                    priority=100  # Normal priority for bulk blocks
                )

                rule = await self.create_blocking_rule(db, rule_data=rule_data, user_id=user_id)
                rules.append(rule)

            except Exception as e:
                logger.error(f"Error creating bulk rule for {value}: {e}")
                continue

        return rules
    
    async def check_blocking_rules(
        self,
        db: Session,
        *,
        website_id: UUID,
        request_data: Dict[str, Any]
    ) -> Tuple[bool, Optional[SecurityRule], Optional[str]]:
        """Check if a request should be blocked"""
        try:
            # Get active rules for website, ordered by priority
            rules = db.query(SecurityRule).filter(
                and_(
                    SecurityRule.website_id == website_id,
                    SecurityRule.is_active == True,
                    or_(
                        SecurityRule.expires_at.is_(None),
                        SecurityRule.expires_at > datetime.utcnow()
                    )
                )
            ).order_by(SecurityRule.priority).all()

            # Check whitelist first
            if await self._is_whitelisted(db, website_id, request_data):
                return False, None, "whitelisted"

            # Check each rule
            for rule in rules:
                if await self._rule_matches(rule, request_data):
                    # Update rule statistics
                    rule.trigger_count += 1
                    rule.last_triggered = datetime.utcnow()
                    db.commit()

                    # Log blocking event
                    await self._log_blocking_event(db, rule, request_data)

                    return True, rule, rule.action

            return False, None, None

        except Exception as e:
            logger.error(f"Error checking blocking rules: {e}")
            return False, None, None

    async def _validate_rule_value(self, rule_type: BlockingRuleType, value: str):
        """Validate rule value based on type"""
        if rule_type == BlockingRuleType.IP_ADDRESS:
            try:
                ipaddress.ip_address(value)
            except ValueError:
                raise ValueError("Invalid IP address format")

        elif rule_type == BlockingRuleType.IP_RANGE:
            try:
                ipaddress.ip_network(value, strict=False)
            except ValueError:
                raise ValueError("Invalid IP range format")

        elif rule_type == BlockingRuleType.COUNTRY:
            if len(value) != 2:
                raise ValueError("Country code must be 2 characters")

        elif rule_type == BlockingRuleType.USER_AGENT:
            if len(value) < 3:
                raise ValueError("User agent pattern too short")

    async def _rule_matches(self, rule: SecurityRule, request_data: Dict[str, Any]) -> bool:
        """Check if a rule matches the request"""
        try:
            if rule.rule_type == BlockingRuleType.IP_ADDRESS:
                return request_data.get('ip_address') == rule.rule_value

            elif rule.rule_type == BlockingRuleType.IP_RANGE:
                ip = request_data.get('ip_address')
                if ip:
                    try:
                        return ipaddress.ip_address(ip) in ipaddress.ip_network(rule.rule_value, strict=False)
                    except ValueError:
                        return False

            elif rule.rule_type == BlockingRuleType.COUNTRY:
                return request_data.get('country') == rule.rule_value

            elif rule.rule_type == BlockingRuleType.USER_AGENT:
                user_agent = request_data.get('user_agent', '')
                if rule.rule_pattern:
                    return bool(re.search(rule.rule_pattern, user_agent, re.IGNORECASE))
                else:
                    return rule.rule_value.lower() in user_agent.lower()

            elif rule.rule_type == BlockingRuleType.RATE_LIMIT:
                return await self._check_rate_limit(rule, request_data)

            return False

        except Exception as e:
            logger.error(f"Error matching rule {rule.id}: {e}")
            return False

    async def _is_whitelisted(self, db: Session, website_id: UUID, request_data: Dict[str, Any]) -> bool:
        """Check if request is whitelisted"""
        try:
            whitelist_entries = db.query(WhitelistEntry).filter(
                and_(
                    WhitelistEntry.website_id == website_id,
                    WhitelistEntry.is_active == True,
                    or_(
                        WhitelistEntry.expires_at.is_(None),
                        WhitelistEntry.expires_at > datetime.utcnow()
                    )
                )
            ).all()

            for entry in whitelist_entries:
                if entry.entry_type == "ip_address" and entry.entry_value == request_data.get('ip_address'):
                    # Update usage statistics
                    entry.usage_count += 1
                    entry.last_used = datetime.utcnow()
                    db.commit()
                    return True

                elif entry.entry_type == "country" and entry.entry_value == request_data.get('country'):
                    entry.usage_count += 1
                    entry.last_used = datetime.utcnow()
                    db.commit()
                    return True

            return False

        except Exception as e:
            logger.error(f"Error checking whitelist: {e}")
            return False

    async def _check_rate_limit(self, rule: SecurityRule, request_data: Dict[str, Any]) -> bool:
        """Check rate limiting"""
        # This would implement rate limiting logic
        # For now, return False (not implemented)
        return False

    async def _log_blocking_event(self, db: Session, rule: SecurityRule, request_data: Dict[str, Any]):
        """Log a blocking event"""
        try:
            # Get IP intelligence
            ip_intelligence = None
            if request_data.get('ip_address'):
                ip_intelligence = await geolocation_service.get_ip_intelligence(request_data['ip_address'])

            event = BlockingEvent(
                website_id=rule.website_id,
                security_rule_id=rule.id,
                event_type="blocked",
                source_ip=request_data.get('ip_address', ''),
                user_agent=request_data.get('user_agent'),
                request_url=request_data.get('url'),
                request_method=request_data.get('method'),
                country=ip_intelligence.country_code if ip_intelligence else None,
                region=ip_intelligence.region if ip_intelligence else None,
                city=ip_intelligence.city if ip_intelligence else None,
                isp=ip_intelligence.isp if ip_intelligence else None,
                is_vpn=ip_intelligence.is_vpn if ip_intelligence else False,
                is_proxy=ip_intelligence.is_proxy if ip_intelligence else False,
                is_tor=ip_intelligence.is_tor if ip_intelligence else False,
                action_taken=rule.action,
                action_details={"rule_name": rule.name, "rule_type": rule.rule_type},
                event_metadata=request_data
            )

            db.add(event)
            db.commit()

            # Emit real-time event
            asyncio.create_task(event_service.emit_system_event(
                event_type="request_blocked",
                message=f"Request blocked by rule '{rule.name}'",
                website_id=rule.website_id,
                alert_level="warning",
                component="blocking_system",
                details={
                    "rule_id": str(rule.id),
                    "source_ip": request_data.get('ip_address'),
                    "action": rule.action
                }
            ))

        except Exception as e:
            logger.error(f"Error logging blocking event: {e}")


# Global blocking service instance
blocking_service = BlockingService()
