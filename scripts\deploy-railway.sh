#!/bin/bash

# ProSecurity Monitor - Railway Deployment Script
# This script automates the deployment process to Railway

set -e  # Exit on any error

echo "🚂 ProSecurity Monitor - Railway Deployment"
echo "=========================================="

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Installing..."
    curl -fsSL https://railway.app/install.sh | sh
    export PATH="$HOME/.railway/bin:$PATH"
fi

# Check if logged in to Railway
if ! railway whoami &> /dev/null; then
    echo "🔐 Please login to Railway..."
    railway login
fi

# Check if we're in a Railway project
if [ ! -f "railway.json" ]; then
    echo "❌ railway.json not found. Initializing Railway project..."
    railway init
fi

# Set environment variables if not already set
echo "🔧 Setting up environment variables..."

# Check for required environment variables
if [ -z "$SECRET_KEY" ]; then
    echo "⚠️  SECRET_KEY not set. Generating random key..."
    SECRET_KEY=$(openssl rand -hex 32)
    railway variables set SECRET_KEY="$SECRET_KEY"
fi

# Add PostgreSQL if not exists
echo "🗄️  Setting up PostgreSQL..."
railway add postgresql || echo "PostgreSQL already exists"

# Add Redis if not exists
echo "🔴 Setting up Redis..."
railway add redis || echo "Redis already exists"

# Deploy the application
echo "🚀 Deploying to Railway..."
railway up

# Get the deployment URL
DEPLOYMENT_URL=$(railway status --json | jq -r '.deployments[0].url')

echo ""
echo "✅ Deployment completed successfully!"
echo "🌐 Your application is available at: $DEPLOYMENT_URL"
echo "📚 API Documentation: $DEPLOYMENT_URL/docs"
echo "🏥 Health Check: $DEPLOYMENT_URL/health"
echo ""
echo "🔍 To view logs: railway logs"
echo "📊 To view status: railway status"
