#!/bin/bash

# ProSecurity Monitor - Development Environment Setup Script
# This script sets up the local development environment

set -e  # Exit on any error

echo "🛠️  ProSecurity Monitor - Development Setup"
echo "=========================================="

# Check Python version
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.11"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python 3.11+ required. Current version: $python_version"
    exit 1
fi

echo "✅ Python version check passed: $python_version"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "🐍 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "📦 Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📚 Installing dependencies..."
pip install -r requirements.txt

# Copy environment file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "⚙️  Creating .env file from template..."
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration"
fi

# Check if Docker is installed
if command -v docker &> /dev/null; then
    echo "🐳 Docker found. Setting up development services..."
    
    # Start development services
    docker-compose up -d db redis
    
    echo "⏳ Waiting for services to be ready..."
    sleep 10
    
    # Run database migrations (when implemented)
    # echo "🗄️  Running database migrations..."
    # alembic upgrade head
    
    echo ""
    echo "✅ Development environment setup completed!"
    echo ""
    echo "🚀 To start the development server:"
    echo "   source venv/bin/activate"
    echo "   uvicorn app.main:app --reload"
    echo ""
    echo "🌐 Application will be available at: http://localhost:8000"
    echo "📚 API Documentation: http://localhost:8000/docs"
    echo "🔴 Redis: localhost:6379"
    echo "🗄️  PostgreSQL: localhost:5432"
    echo "🌸 Flower (Celery): http://localhost:5555"
    echo ""
    echo "🛑 To stop services: docker-compose down"
    
else
    echo "⚠️  Docker not found. Please install Docker to run development services."
    echo "✅ Python environment setup completed!"
    echo ""
    echo "🚀 To start the development server:"
    echo "   source venv/bin/activate"
    echo "   uvicorn app.main:app --reload"
fi
