"""
Blocking system schemas
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
from enum import Enum

from app.models.blocking import BlockingRuleType, BlockingAction


# Base schemas
class BlockingRuleBase(BaseModel):
    """Base blocking rule schema"""
    name: str = Field(..., min_length=1, max_length=255, description="Rule name")
    description: Optional[str] = Field(None, description="Rule description")
    rule_type: BlockingRuleType = Field(..., description="Type of blocking rule")
    action: BlockingAction = Field(..., description="Action to take when rule is triggered")
    rule_value: str = Field(..., min_length=1, max_length=500, description="Rule value (IP, country, pattern)")
    rule_pattern: Optional[str] = Field(None, max_length=1000, description="Regex pattern for custom rules")
    rule_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional rule configuration")
    is_temporary: bool = Field(False, description="Whether the rule is temporary")
    expires_at: Optional[datetime] = Field(None, description="When the rule expires")
    rate_limit_requests: Optional[int] = Field(None, ge=1, description="Requests per time window")
    rate_limit_window: Optional[int] = Field(None, ge=1, description="Time window in seconds")
    is_active: bool = Field(True, description="Whether the rule is active")
    priority: int = Field(100, ge=1, le=1000, description="Rule priority (lower = higher priority)")


class BlockingRuleCreate(BlockingRuleBase):
    """Schema for creating blocking rules"""
    website_id: UUID = Field(..., description="Website ID")
    
    @validator('expires_at')
    def validate_expires_at(cls, v, values):
        if values.get('is_temporary') and not v:
            raise ValueError('expires_at is required for temporary rules')
        if not values.get('is_temporary') and v:
            raise ValueError('expires_at should not be set for permanent rules')
        return v
    
    @validator('rate_limit_requests')
    def validate_rate_limit(cls, v, values):
        if values.get('action') == BlockingAction.THROTTLE and not v:
            raise ValueError('rate_limit_requests is required for throttle action')
        return v


class BlockingRuleUpdate(BaseModel):
    """Schema for updating blocking rules"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    rule_value: Optional[str] = Field(None, min_length=1, max_length=500)
    rule_pattern: Optional[str] = Field(None, max_length=1000)
    rule_metadata: Optional[Dict[str, Any]] = None
    is_temporary: Optional[bool] = None
    expires_at: Optional[datetime] = None
    rate_limit_requests: Optional[int] = Field(None, ge=1)
    rate_limit_window: Optional[int] = Field(None, ge=1)
    is_active: Optional[bool] = None
    priority: Optional[int] = Field(None, ge=1, le=1000)


class BlockingRuleResponse(BlockingRuleBase):
    """Schema for blocking rule responses"""
    id: UUID
    website_id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str]
    trigger_count: int
    last_triggered: Optional[datetime]
    
    class Config:
        from_attributes = True


class BlockingRuleListResponse(BaseModel):
    """Schema for blocking rule list responses"""
    rules: List[BlockingRuleResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


# Whitelist schemas
class WhitelistEntryBase(BaseModel):
    """Base whitelist entry schema"""
    entry_type: str = Field(..., description="Type of whitelist entry")
    entry_value: str = Field(..., min_length=1, max_length=500, description="Whitelist value")
    description: Optional[str] = Field(None, description="Entry description")
    is_active: bool = Field(True, description="Whether the entry is active")
    expires_at: Optional[datetime] = Field(None, description="When the entry expires")


class WhitelistEntryCreate(WhitelistEntryBase):
    """Schema for creating whitelist entries"""
    website_id: UUID = Field(..., description="Website ID")


class WhitelistEntryUpdate(BaseModel):
    """Schema for updating whitelist entries"""
    entry_value: Optional[str] = Field(None, min_length=1, max_length=500)
    description: Optional[str] = None
    is_active: Optional[bool] = None
    expires_at: Optional[datetime] = None


class WhitelistEntryResponse(WhitelistEntryBase):
    """Schema for whitelist entry responses"""
    id: UUID
    website_id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str]
    usage_count: int
    last_used: Optional[datetime]
    
    class Config:
        from_attributes = True


class WhitelistListResponse(BaseModel):
    """Schema for whitelist list responses"""
    entries: List[WhitelistEntryResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


# Blocking event schemas
class BlockingEventResponse(BaseModel):
    """Schema for blocking event responses"""
    id: UUID
    website_id: UUID
    blocking_rule_id: Optional[UUID]
    event_type: str
    source_ip: str
    user_agent: Optional[str]
    request_url: Optional[str]
    request_method: Optional[str]
    country: Optional[str]
    region: Optional[str]
    city: Optional[str]
    isp: Optional[str]
    is_vpn: bool
    is_proxy: bool
    is_tor: bool
    action_taken: str
    action_details: Optional[Dict[str, Any]]
    blocked_at: datetime
    duration_seconds: Optional[int]
    event_metadata: Optional[Dict[str, Any]]
    
    class Config:
        from_attributes = True


class BlockingEventListResponse(BaseModel):
    """Schema for blocking event list responses"""
    events: List[BlockingEventResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


# Quick action schemas
class IPBlockRequest(BaseModel):
    """Schema for blocking specific IP addresses"""
    ip_address: str = Field(..., description="IP address to block")
    website_id: UUID = Field(..., description="Website ID")
    reason: Optional[str] = Field(None, description="Reason for blocking")
    is_temporary: bool = Field(False, description="Whether the block is temporary")
    expires_at: Optional[datetime] = Field(None, description="When the block expires")
    
    @validator('ip_address')
    def validate_ip_address(cls, v):
        import ipaddress
        try:
            ipaddress.ip_address(v)
        except ValueError:
            raise ValueError('Invalid IP address format')
        return v


class CountryBlockRequest(BaseModel):
    """Schema for blocking by country"""
    country_code: str = Field(..., min_length=2, max_length=2, description="ISO country code")
    website_id: UUID = Field(..., description="Website ID")
    reason: Optional[str] = Field(None, description="Reason for blocking")
    is_temporary: bool = Field(False, description="Whether the block is temporary")
    expires_at: Optional[datetime] = Field(None, description="When the block expires")
    
    @validator('country_code')
    def validate_country_code(cls, v):
        return v.upper()


class BulkBlockRequest(BaseModel):
    """Schema for bulk blocking operations"""
    website_id: UUID = Field(..., description="Website ID")
    rule_type: BlockingRuleType = Field(..., description="Type of blocking rule")
    values: List[str] = Field(..., min_items=1, max_items=100, description="Values to block")
    reason: Optional[str] = Field(None, description="Reason for blocking")
    is_temporary: bool = Field(False, description="Whether the blocks are temporary")
    expires_at: Optional[datetime] = Field(None, description="When the blocks expire")


# Analytics schemas
class BlockingAnalytics(BaseModel):
    """Schema for blocking analytics"""
    total_rules: int
    active_rules: int
    temporary_rules: int
    total_blocks_today: int
    total_blocks_week: int
    total_blocks_month: int
    top_blocked_countries: List[Dict[str, Any]]
    top_blocked_ips: List[Dict[str, Any]]
    blocking_trends: List[Dict[str, Any]]
    rule_effectiveness: List[Dict[str, Any]]


class RateLimitStatus(BaseModel):
    """Schema for rate limit status"""
    tracking_key: str
    tracking_type: str
    request_count: int
    window_start: datetime
    window_duration: int
    is_blocked: bool
    blocked_until: Optional[datetime]
    remaining_requests: int
    reset_time: datetime
