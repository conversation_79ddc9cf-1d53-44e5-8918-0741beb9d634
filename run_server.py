#!/usr/bin/env python3
"""
Railway-compatible server runner for ProSecurity Monitor
Handles PORT environment variable properly
"""

import os
import uvicorn

def main():
    """Run the FastAPI server with proper PORT handling"""
    # Get port from environment variable, default to 8000
    port = 8000
    port_env = os.getenv("PORT")
    
    if port_env:
        try:
            port = int(port_env)
            print(f"🚀 Using PORT from environment: {port}")
        except (ValueError, TypeError):
            print(f"⚠️  Invalid PORT value '{port_env}', using default: {port}")
    else:
        print(f"🔧 No PORT environment variable, using default: {port}")
    
    # Run the server
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=port,
        log_level="info"
    )

if __name__ == "__main__":
    main()
