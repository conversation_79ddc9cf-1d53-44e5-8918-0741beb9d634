"""
Machine Learning models for threat detection (Mock implementation)
"""

import pickle
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
from pathlib import Path
import logging

# Mock imports for ML dependencies
try:
    import numpy as np
    from sklearn.ensemble import IsolationForest, RandomForestClassifier
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import classification_report, confusion_matrix
    import joblib
    ML_AVAILABLE = True
except ImportError:
    # Mock classes for when ML dependencies are not available
    class MockModel:
        def __init__(self, *args, **kwargs):
            pass
        def fit(self, *args, **kwargs):
            pass
        def predict(self, *args, **kwargs):
            return [0]
        def predict_proba(self, *args, **kwargs):
            return [[0.5, 0.5]]
        def decision_function(self, *args, **kwargs):
            return [0.0]

    class MockScaler:
        def fit(self, *args, **kwargs):
            pass
        def transform(self, data):
            return data

    IsolationForest = MockModel
    RandomForestClassifier = MockModel
    StandardScaler = MockScaler
    np = None
    joblib = None
    ML_AVAILABLE = False

from app.ml.features import RequestFeatures, FeatureExtractor

logger = logging.getLogger(__name__)


class ThreatModel:
    """Base class for threat detection models"""
    
    def __init__(self, model_name: str):
        self.model_name = model_name
        self.model = None
        self.scaler = None
        self.feature_extractor = FeatureExtractor()
        self.is_trained = False
        self.model_version = "1.0"
        self.last_updated = None
        
    def predict(self, features: RequestFeatures) -> Tuple[bool, float]:
        """
        Predict if request is malicious
        Returns: (is_threat, confidence_score)
        """
        raise NotImplementedError
    
    def predict_batch(self, features_list: List[RequestFeatures]) -> List[Tuple[bool, float]]:
        """Predict batch of requests"""
        return [self.predict(features) for features in features_list]
    
    def train(self, training_data: List[Tuple[RequestFeatures, bool]]):
        """Train the model with labeled data"""
        raise NotImplementedError
    
    def save_model(self, path: str):
        """Save model to disk"""
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'model_name': self.model_name,
            'model_version': self.model_version,
            'is_trained': self.is_trained,
            'last_updated': self.last_updated
        }
        
        joblib.dump(model_data, path)
        logger.info(f"Model {self.model_name} saved to {path}")
    
    def load_model(self, path: str):
        """Load model from disk"""
        try:
            model_data = joblib.load(path)
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.model_name = model_data['model_name']
            self.model_version = model_data['model_version']
            self.is_trained = model_data['is_trained']
            self.last_updated = model_data['last_updated']
            
            logger.info(f"Model {self.model_name} loaded from {path}")
            return True
        except Exception as e:
            logger.error(f"Error loading model from {path}: {e}")
            return False


class AnomalyDetector(ThreatModel):
    """Anomaly detection model using Isolation Forest"""
    
    def __init__(self):
        super().__init__("anomaly_detector")
        self.model = IsolationForest(
            contamination=0.1,  # Expected proportion of anomalies
            random_state=42,
            n_estimators=100
        )
        self.scaler = StandardScaler()
    
    def predict(self, features: RequestFeatures) -> Tuple[bool, float]:
        """Predict if request is anomalous"""
        if not self.is_trained:
            # Use rule-based fallback
            return self._rule_based_prediction(features)
        
        try:
            # Convert features to vector
            feature_vector = self.feature_extractor.features_to_vector(features)
            feature_vector = feature_vector.reshape(1, -1)
            
            # Scale features
            feature_vector_scaled = self.scaler.transform(feature_vector)
            
            # Predict anomaly
            anomaly_score = self.model.decision_function(feature_vector_scaled)[0]
            is_anomaly = self.model.predict(feature_vector_scaled)[0] == -1
            
            # Convert anomaly score to confidence (0-100)
            # Isolation Forest returns negative scores for anomalies
            confidence = min(100, max(0, abs(anomaly_score) * 50))
            
            return is_anomaly, confidence
            
        except Exception as e:
            logger.error(f"Error in anomaly prediction: {e}")
            return self._rule_based_prediction(features)
    
    def train(self, training_data: List[Tuple[RequestFeatures, bool]]):
        """Train anomaly detector with normal traffic data"""
        if not ML_AVAILABLE:
            logger.warning("ML dependencies not available, using rule-based detection")
            self.is_trained = True  # Mark as trained to use rule-based fallback
            self.last_updated = datetime.utcnow()
            return True

        try:
            # Extract feature vectors from normal traffic only
            normal_features = []
            for features, is_threat in training_data:
                if not is_threat:  # Only use normal traffic for anomaly detection
                    feature_vector = self.feature_extractor.features_to_vector(features)
                    normal_features.append(feature_vector)

            if len(normal_features) < 10:
                logger.warning("Insufficient normal traffic data for training")
                return False

            X = np.array(normal_features)

            # Fit scaler
            self.scaler.fit(X)
            X_scaled = self.scaler.transform(X)

            # Train model
            self.model.fit(X_scaled)

            self.is_trained = True
            self.last_updated = datetime.utcnow()

            logger.info(f"Anomaly detector trained with {len(normal_features)} normal samples")
            return True

        except Exception as e:
            logger.error(f"Error training anomaly detector: {e}")
            return False
    
    def _rule_based_prediction(self, features: RequestFeatures) -> Tuple[bool, float]:
        """Fallback rule-based prediction when model is not trained"""
        threat_score = 0
        
        # Check for obvious threats
        if features.has_sql_keywords:
            threat_score += 40
        if features.has_script_tags:
            threat_score += 35
        if features.url_suspicious_chars > 10:
            threat_score += 20
        if features.param_entropy > 4.0:
            threat_score += 15
        if features.is_tor or features.is_vpn:
            threat_score += 10
        if features.method_risk_score > 0.5:
            threat_score += 10
        
        is_threat = threat_score > 50
        confidence = min(100, threat_score)
        
        return is_threat, confidence


class BehaviorAnalyzer(ThreatModel):
    """Behavioral analysis model using Random Forest"""
    
    def __init__(self):
        super().__init__("behavior_analyzer")
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            class_weight='balanced'
        )
        self.scaler = StandardScaler()
    
    def predict(self, features: RequestFeatures) -> Tuple[bool, float]:
        """Predict if request shows malicious behavior"""
        if not self.is_trained:
            return self._behavioral_rules(features)
        
        try:
            # Convert features to vector
            feature_vector = self.feature_extractor.features_to_vector(features)
            feature_vector = feature_vector.reshape(1, -1)
            
            # Scale features
            feature_vector_scaled = self.scaler.transform(feature_vector)
            
            # Predict
            prediction = self.model.predict(feature_vector_scaled)[0]
            probabilities = self.model.predict_proba(feature_vector_scaled)[0]
            
            is_threat = bool(prediction)
            confidence = max(probabilities) * 100
            
            return is_threat, confidence
            
        except Exception as e:
            logger.error(f"Error in behavior prediction: {e}")
            return self._behavioral_rules(features)
    
    def train(self, training_data: List[Tuple[RequestFeatures, bool]]):
        """Train behavioral model with labeled data"""
        if not ML_AVAILABLE:
            logger.warning("ML dependencies not available, using rule-based detection")
            self.is_trained = True  # Mark as trained to use rule-based fallback
            self.last_updated = datetime.utcnow()
            return True

        try:
            if len(training_data) < 50:
                logger.warning("Insufficient training data for behavioral model")
                return False

            # Extract features and labels
            X = []
            y = []

            for features, is_threat in training_data:
                feature_vector = self.feature_extractor.features_to_vector(features)
                X.append(feature_vector)
                y.append(int(is_threat))

            X = np.array(X)
            y = np.array(y)

            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )

            # Fit scaler
            self.scaler.fit(X_train)
            X_train_scaled = self.scaler.transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)

            # Train model
            self.model.fit(X_train_scaled, y_train)

            # Evaluate
            y_pred = self.model.predict(X_test_scaled)
            accuracy = np.mean(y_pred == y_test)

            self.is_trained = True
            self.last_updated = datetime.utcnow()

            logger.info(f"Behavioral model trained with accuracy: {accuracy:.3f}")
            logger.info(f"Training data: {len(training_data)} samples")

            return True

        except Exception as e:
            logger.error(f"Error training behavioral model: {e}")
            return False
    
    def _behavioral_rules(self, features: RequestFeatures) -> Tuple[bool, float]:
        """Rule-based behavioral analysis"""
        threat_score = 0
        
        # High request frequency
        if features.request_frequency > 10:
            threat_score += 25
        
        # High error rate
        if features.error_rate > 0.5:
            threat_score += 20
        
        # Suspicious timing
        if features.is_night_time and not features.is_weekend:
            threat_score += 10
        
        # Bot-like behavior
        if features.is_bot_user_agent:
            threat_score += 15
        
        # Missing common headers (bot-like)
        if features.missing_common_headers > 3:
            threat_score += 15
        
        # High entropy in parameters (obfuscation)
        if features.param_entropy > 5.0:
            threat_score += 20
        
        is_threat = threat_score > 40
        confidence = min(100, threat_score)
        
        return is_threat, confidence


class EnsembleDetector(ThreatModel):
    """Ensemble model combining multiple detectors"""
    
    def __init__(self):
        super().__init__("ensemble_detector")
        self.anomaly_detector = AnomalyDetector()
        self.behavior_analyzer = BehaviorAnalyzer()
        self.weights = {
            'anomaly': 0.4,
            'behavior': 0.6
        }
    
    def predict(self, features: RequestFeatures) -> Tuple[bool, float]:
        """Ensemble prediction combining multiple models"""
        try:
            # Get predictions from individual models
            anomaly_threat, anomaly_conf = self.anomaly_detector.predict(features)
            behavior_threat, behavior_conf = self.behavior_analyzer.predict(features)
            
            # Weighted ensemble
            ensemble_score = (
                self.weights['anomaly'] * anomaly_conf +
                self.weights['behavior'] * behavior_conf
            )
            
            # Threat if either model predicts threat with high confidence
            # or ensemble score is high
            is_threat = (
                (anomaly_threat and anomaly_conf > 70) or
                (behavior_threat and behavior_conf > 70) or
                ensemble_score > 60
            )
            
            confidence = min(100, ensemble_score)
            
            return is_threat, confidence
            
        except Exception as e:
            logger.error(f"Error in ensemble prediction: {e}")
            # Fallback to simple rule-based detection
            return self._simple_rules(features)
    
    def train(self, training_data: List[Tuple[RequestFeatures, bool]]):
        """Train all component models"""
        anomaly_success = self.anomaly_detector.train(training_data)
        behavior_success = self.behavior_analyzer.train(training_data)
        
        self.is_trained = anomaly_success or behavior_success
        self.last_updated = datetime.utcnow()
        
        return self.is_trained
    
    def save_model(self, path: str):
        """Save ensemble model"""
        # Save individual models
        base_path = Path(path)
        anomaly_path = base_path.parent / f"{base_path.stem}_anomaly{base_path.suffix}"
        behavior_path = base_path.parent / f"{base_path.stem}_behavior{base_path.suffix}"
        
        self.anomaly_detector.save_model(str(anomaly_path))
        self.behavior_analyzer.save_model(str(behavior_path))
        
        # Save ensemble metadata
        ensemble_data = {
            'model_name': self.model_name,
            'model_version': self.model_version,
            'weights': self.weights,
            'is_trained': self.is_trained,
            'last_updated': self.last_updated
        }
        
        joblib.dump(ensemble_data, path)
    
    def load_model(self, path: str):
        """Load ensemble model"""
        try:
            # Load ensemble metadata
            ensemble_data = joblib.load(path)
            self.weights = ensemble_data['weights']
            self.is_trained = ensemble_data['is_trained']
            self.last_updated = ensemble_data['last_updated']
            
            # Load individual models
            base_path = Path(path)
            anomaly_path = base_path.parent / f"{base_path.stem}_anomaly{base_path.suffix}"
            behavior_path = base_path.parent / f"{base_path.stem}_behavior{base_path.suffix}"
            
            self.anomaly_detector.load_model(str(anomaly_path))
            self.behavior_analyzer.load_model(str(behavior_path))
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading ensemble model: {e}")
            return False
    
    def _simple_rules(self, features: RequestFeatures) -> Tuple[bool, float]:
        """Simple rule-based fallback"""
        threat_indicators = 0
        
        if features.has_sql_keywords:
            threat_indicators += 3
        if features.has_script_tags:
            threat_indicators += 3
        if features.url_suspicious_chars > 5:
            threat_indicators += 2
        if features.param_entropy > 4.0:
            threat_indicators += 2
        if features.is_tor:
            threat_indicators += 2
        if features.request_frequency > 5:
            threat_indicators += 1
        
        is_threat = threat_indicators >= 3
        confidence = min(100, threat_indicators * 15)
        
        return is_threat, confidence
