"""
Database analysis endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any
from uuid import UUID

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.services.database_service import database_service

router = APIRouter()


@router.post("/connect")
async def test_database_connection(
    connection_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Test database connection"""
    try:
        result = await database_service.test_connection(
            db, connection_data=connection_data, user_id=current_user.id
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Connection failed")


@router.post("/scan")
async def start_database_scan(
    website_id: UUID,
    connection_data: Dict[str, Any],
    scan_type: str = "full",
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start database structure analysis"""
    try:
        scan = await database_service.start_database_analysis(
            db, website_id=website_id, connection_data=connection_data,
            user_id=current_user.id, scan_type=scan_type
        )
        return {
            "scan_id": scan.id,
            "status": scan.status,
            "message": "Database analysis started"
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Analysis failed")


@router.get("/schema/{scan_id}")
async def get_database_schema(
    scan_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get database schema information from scan"""
    try:
        schema_data = await database_service.get_database_schema(
            db, scan_id=scan_id, user_id=current_user.id
        )
        return schema_data
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/vulnerabilities/{scan_id}")
async def get_database_vulnerabilities(
    scan_id: UUID,
    severity: Optional[str] = Query(None, description="Filter by severity"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get database security vulnerabilities from scan"""
    try:
        vulnerabilities = await database_service.get_database_vulnerabilities(
            db, scan_id=scan_id, user_id=current_user.id,
            severity=severity, skip=skip, limit=limit
        )
        return vulnerabilities
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/relationships/{scan_id}")
async def get_database_relationships(
    scan_id: UUID,
    table_name: Optional[str] = Query(None, description="Filter by table name"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get database table relationships from scan"""
    try:
        relationships = await database_service.get_database_relationships(
            db, scan_id=scan_id, user_id=current_user.id, table_name=table_name
        )
        return relationships
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")
