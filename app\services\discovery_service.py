"""
Discovery service for URL discovery and web crawling
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from app.services.base_service import BaseService
from app.services.event_service import event_service
from app.models.scan import Scan
from app.models.website import Website
from app.schemas.scan import ScanCreate

logger = logging.getLogger(__name__)


class DiscoveryService(BaseService[Scan, ScanCreate, Dict]):
    """Service for URL discovery and web crawling"""
    
    def __init__(self):
        super().__init__(Scan)
    
    async def start_discovery_scan(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID,
        scan_type: str = "full",
        max_depth: int = 3
    ) -> Scan:
        """Start a URL discovery scan"""
        # Verify website ownership
        website = db.query(Website).filter(
            and_(Website.id == website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise ValueError("Website not found")
        
        # Create scan record
        scan_data = ScanCreate(
            website_id=website_id,
            scan_type="url_discovery",
            status="pending",
            scan_config={
                "scan_type": scan_type,
                "max_depth": max_depth,
                "follow_redirects": True,
                "respect_robots_txt": True
            }
        )
        
        scan = await self.create(db, obj_in=scan_data)
        
        # Emit event
        asyncio.create_task(event_service.emit_scan_event(
            event_type="scan_started",
            scan_id=scan.id,
            website_id=website_id,
            scan_type="url_discovery",
            message="URL discovery scan started"
        ))
        
        # Start background scan task
        from app.tasks.scan_tasks import perform_url_discovery_task
        perform_url_discovery_task.delay(str(scan.id), website.domain)
        
        return scan
    
    async def get_scan_results(
        self,
        db: Session,
        *,
        scan_id: UUID,
        user_id: UUID
    ) -> Optional[Scan]:
        """Get discovery scan results"""
        scan = db.query(Scan).filter(
            and_(
                Scan.id == scan_id,
                Scan.website.has(Website.user_id == user_id)
            )
        ).first()
        
        return scan
    
    async def get_website_sitemap(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID,
        format: str = "json"
    ) -> Any:
        """Get website sitemap from discovery results"""
        # Verify website ownership
        website = db.query(Website).filter(
            and_(Website.id == website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise ValueError("Website not found")
        
        # Get latest completed discovery scan
        scan = db.query(Scan).filter(
            and_(
                Scan.website_id == website_id,
                Scan.scan_type == "url_discovery",
                Scan.status == "completed"
            )
        ).order_by(desc(Scan.completed_at)).first()
        
        if not scan or not scan.results:
            return {"urls": [], "total": 0} if format == "json" else "<urlset></urlset>"
        
        urls = scan.results.get("urls", [])
        
        if format == "xml":
            # Generate XML sitemap
            xml_content = '<?xml version="1.0" encoding="UTF-8"?>\n'
            xml_content += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'
            
            for url_data in urls:
                xml_content += f'  <url>\n'
                xml_content += f'    <loc>{url_data.get("url", "")}</loc>\n'
                xml_content += f'    <lastmod>{url_data.get("discovered_at", "")}</lastmod>\n'
                xml_content += f'    <priority>0.5</priority>\n'
                xml_content += f'  </url>\n'
            
            xml_content += '</urlset>'
            return xml_content
        
        return {
            "urls": urls,
            "total": len(urls),
            "website_id": str(website_id),
            "generated_at": datetime.utcnow().isoformat()
        }
    
    async def get_discovered_forms(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID,
        skip: int = 0,
        limit: int = 100
    ) -> Dict[str, Any]:
        """Get discovered forms for a website"""
        # Verify website ownership
        website = db.query(Website).filter(
            and_(Website.id == website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise ValueError("Website not found")
        
        # Get latest completed discovery scan
        scan = db.query(Scan).filter(
            and_(
                Scan.website_id == website_id,
                Scan.scan_type == "url_discovery",
                Scan.status == "completed"
            )
        ).order_by(desc(Scan.completed_at)).first()
        
        if not scan or not scan.results:
            return {"forms": [], "total": 0, "page": 1, "per_page": limit}
        
        forms = scan.results.get("forms_discovered", [])
        total = len(forms)
        
        # Apply pagination
        paginated_forms = forms[skip:skip + limit]
        
        return {
            "forms": paginated_forms,
            "total": total,
            "page": (skip // limit) + 1,
            "per_page": limit,
            "has_next": skip + limit < total,
            "has_prev": skip > 0
        }
    
    async def get_discovered_apis(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID,
        skip: int = 0,
        limit: int = 100
    ) -> Dict[str, Any]:
        """Get discovered API endpoints for a website"""
        # Verify website ownership
        website = db.query(Website).filter(
            and_(Website.id == website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise ValueError("Website not found")
        
        # Get latest completed discovery scan
        scan = db.query(Scan).filter(
            and_(
                Scan.website_id == website_id,
                Scan.scan_type == "url_discovery",
                Scan.status == "completed"
            )
        ).order_by(desc(Scan.completed_at)).first()
        
        if not scan or not scan.results:
            return {"apis": [], "total": 0, "page": 1, "per_page": limit}
        
        apis = scan.results.get("api_endpoints", [])
        total = len(apis)
        
        # Apply pagination
        paginated_apis = apis[skip:skip + limit]
        
        return {
            "apis": paginated_apis,
            "total": total,
            "page": (skip // limit) + 1,
            "per_page": limit,
            "has_next": skip + limit < total,
            "has_prev": skip > 0
        }
    
    async def get_discovered_technologies(
        self,
        db: Session,
        *,
        website_id: UUID,
        user_id: UUID
    ) -> Dict[str, Any]:
        """Get discovered technologies for a website"""
        # Verify website ownership
        website = db.query(Website).filter(
            and_(Website.id == website_id, Website.user_id == user_id)
        ).first()
        
        if not website:
            raise ValueError("Website not found")
        
        # Get latest completed discovery scan
        scan = db.query(Scan).filter(
            and_(
                Scan.website_id == website_id,
                Scan.scan_type == "url_discovery",
                Scan.status == "completed"
            )
        ).order_by(desc(Scan.completed_at)).first()
        
        if not scan or not scan.results:
            return {"technologies": [], "categories": {}}
        
        # Extract technology information from scan results
        technologies = scan.results.get("technologies", [])
        
        # Group by category
        categories = {}
        for tech in technologies:
            category = tech.get("category", "Other")
            if category not in categories:
                categories[category] = []
            categories[category].append(tech)
        
        return {
            "technologies": technologies,
            "categories": categories,
            "total_technologies": len(technologies),
            "website_id": str(website_id),
            "scan_date": scan.completed_at.isoformat() if scan.completed_at else None
        }


# Global discovery service instance
discovery_service = DiscoveryService()
