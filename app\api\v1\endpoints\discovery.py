"""
URL discovery and web crawling endpoints
"""

from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, Query
from fastapi.responses import Response
from sqlalchemy.orm import Session
from typing import Optional
from uuid import UUID

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.services.discovery_service import discovery_service

router = APIRouter()


@router.post("/scan")
async def start_url_discovery(
    website_id: UUID,
    scan_type: str = "full",
    max_depth: int = 3,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start URL discovery scan"""
    try:
        scan = await discovery_service.start_discovery_scan(
            db, website_id=website_id, user_id=current_user.id,
            scan_type=scan_type, max_depth=max_depth
        )
        return {
            "scan_id": scan.id,
            "status": scan.status,
            "message": "URL discovery scan started"
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/results/{scan_id}")
async def get_scan_results(
    scan_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get URL discovery scan results"""
    try:
        results = await discovery_service.get_scan_results(
            db, scan_id=scan_id, user_id=current_user.id
        )
        return results
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/sitemap/{website_id}")
async def get_website_sitemap(
    website_id: UUID,
    format: str = Query("json", description="Response format: json, xml"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get website sitemap from discovery results"""
    try:
        sitemap_data = await discovery_service.get_website_sitemap(
            db, website_id=website_id, user_id=current_user.id, format=format
        )

        if format == "xml":
            return Response(content=sitemap_data, media_type="application/xml")

        return sitemap_data

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/forms/{website_id}")
async def get_discovered_forms(
    website_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get discovered forms for a website"""
    try:
        forms_data = await discovery_service.get_discovered_forms(
            db, website_id=website_id, user_id=current_user.id, skip=skip, limit=limit
        )
        return forms_data

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/apis/{website_id}")
async def get_discovered_apis(
    website_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get discovered API endpoints for a website"""
    try:
        apis_data = await discovery_service.get_discovered_apis(
            db, website_id=website_id, user_id=current_user.id, skip=skip, limit=limit
        )
        return apis_data

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/technologies/{website_id}")
async def get_discovered_technologies(
    website_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get discovered technologies for a website"""
    try:
        tech_data = await discovery_service.get_discovered_technologies(
            db, website_id=website_id, user_id=current_user.id
        )
        return tech_data

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")
