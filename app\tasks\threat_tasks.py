"""
Background tasks for threat analysis and ML training
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List
from uuid import UUID

from celery import current_task
from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from app.tasks.celery_app import celery_app
from app.core.database import SessionLocal
from app.models.threat import Threat
from app.models.visitor import Visitor
from app.models.website import Website
from app.ml.threat_detector import ml_threat_detector
from app.services.event_service import event_service

logger = logging.getLogger(__name__)


def get_db_session():
    """Get database session for tasks"""
    return SessionLocal()


@celery_app.task(bind=True, name='app.tasks.threat_tasks.analyze_threat_patterns_task')
def analyze_threat_patterns_task(self, website_id: str = None, days_back: int = 7):
    """Analyze threat patterns and update risk assessments"""
    
    try:
        self.update_state(state='PROGRESS', meta={'progress': 0, 'status': 'Starting threat pattern analysis'})
        
        db = get_db_session()
        
        try:
            # Date range for analysis
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days_back)
            
            # Build query
            query = db.query(Threat).filter(Threat.detected_at >= start_date)
            if website_id:
                query = query.filter(Threat.website_id == UUID(website_id))
            
            threats = query.all()
            
            self.update_state(
                state='PROGRESS',
                meta={'progress': 25, 'status': f'Analyzing {len(threats)} threats'}
            )
            
            # Analyze threat patterns
            patterns = {
                'threat_types': {},
                'source_countries': {},
                'attack_vectors': {},
                'time_patterns': {},
                'severity_trends': {},
                'ip_patterns': {}
            }
            
            for threat in threats:
                # Threat type distribution
                threat_type = threat.threat_type
                patterns['threat_types'][threat_type] = patterns['threat_types'].get(threat_type, 0) + 1
                
                # Source country distribution
                if threat.source_country:
                    country = threat.source_country
                    patterns['source_countries'][country] = patterns['source_countries'].get(country, 0) + 1
                
                # Attack vector analysis
                if threat.attack_vector:
                    vector = threat.attack_vector
                    patterns['attack_vectors'][vector] = patterns['attack_vectors'].get(vector, 0) + 1
                
                # Time pattern analysis
                hour = threat.detected_at.hour
                patterns['time_patterns'][hour] = patterns['time_patterns'].get(hour, 0) + 1
                
                # Severity trends
                severity = threat.severity
                patterns['severity_trends'][severity] = patterns['severity_trends'].get(severity, 0) + 1
                
                # IP pattern analysis
                if threat.source_ip:
                    ip = threat.source_ip
                    patterns['ip_patterns'][ip] = patterns['ip_patterns'].get(ip, 0) + 1
            
            self.update_state(
                state='PROGRESS',
                meta={'progress': 50, 'status': 'Identifying high-risk patterns'}
            )
            
            # Identify high-risk patterns
            risk_indicators = []
            
            # Check for concentrated attacks from specific IPs
            for ip, count in patterns['ip_patterns'].items():
                if count > 10:  # More than 10 attacks from same IP
                    risk_indicators.append({
                        'type': 'concentrated_ip_attack',
                        'value': ip,
                        'count': count,
                        'risk_level': 'high'
                    })
            
            # Check for attacks from high-risk countries
            high_risk_countries = {'CN', 'RU', 'KP', 'IR'}
            for country, count in patterns['source_countries'].items():
                if country in high_risk_countries and count > 5:
                    risk_indicators.append({
                        'type': 'high_risk_country',
                        'value': country,
                        'count': count,
                        'risk_level': 'medium'
                    })
            
            # Check for unusual time patterns (attacks during off-hours)
            night_attacks = sum(patterns['time_patterns'].get(hour, 0) for hour in range(0, 6))
            total_attacks = len(threats)
            if total_attacks > 0 and (night_attacks / total_attacks) > 0.3:
                risk_indicators.append({
                    'type': 'unusual_time_pattern',
                    'value': 'night_attacks',
                    'percentage': (night_attacks / total_attacks) * 100,
                    'risk_level': 'medium'
                })
            
            self.update_state(
                state='PROGRESS',
                meta={'progress': 75, 'status': 'Generating recommendations'}
            )
            
            # Generate recommendations
            recommendations = []
            
            if patterns['threat_types'].get('sql_injection', 0) > 5:
                recommendations.append("Implement parameterized queries to prevent SQL injection")
            
            if patterns['threat_types'].get('xss', 0) > 3:
                recommendations.append("Add input sanitization and output encoding for XSS prevention")
            
            if len(patterns['ip_patterns']) > 20:
                recommendations.append("Consider implementing IP-based rate limiting")
            
            if patterns['severity_trends'].get('high', 0) > 10:
                recommendations.append("Review and strengthen security controls for high-severity threats")
            
            # Prepare analysis results
            analysis_results = {
                'analysis_period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days_analyzed': days_back
                },
                'threat_summary': {
                    'total_threats': len(threats),
                    'unique_ips': len(patterns['ip_patterns']),
                    'unique_countries': len(patterns['source_countries']),
                    'most_common_type': max(patterns['threat_types'].items(), key=lambda x: x[1])[0] if patterns['threat_types'] else None
                },
                'patterns': patterns,
                'risk_indicators': risk_indicators,
                'recommendations': recommendations,
                'analysis_completed_at': datetime.utcnow().isoformat()
            }
            
            self.update_state(
                state='PROGRESS',
                meta={'progress': 100, 'status': 'Analysis completed'}
            )
            
            return {
                'status': 'completed',
                'website_id': website_id,
                'results': analysis_results
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error in threat pattern analysis: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@celery_app.task(bind=True, name='app.tasks.threat_tasks.update_threat_intelligence_task')
def update_threat_intelligence_task(self):
    """Update threat intelligence from external sources"""
    
    try:
        self.update_state(state='PROGRESS', meta={'progress': 0, 'status': 'Updating threat intelligence'})
        
        # This would integrate with external threat intelligence feeds
        # For now, simulate the process
        
        intelligence_sources = [
            'Tor Exit Nodes',
            'Known Malicious IPs',
            'VPN Provider Lists',
            'Botnet C&C Servers',
            'Phishing Domains'
        ]
        
        updated_sources = []
        
        for i, source in enumerate(intelligence_sources):
            progress = int((i + 1) / len(intelligence_sources) * 100)
            
            self.update_state(
                state='PROGRESS',
                meta={'progress': progress, 'status': f'Updating {source}'}
            )
            
            # Simulate update process
            import time
            time.sleep(1)
            
            updated_sources.append({
                'source': source,
                'updated_at': datetime.utcnow().isoformat(),
                'records_updated': 100 + i * 50  # Mock data
            })
        
        return {
            'status': 'completed',
            'updated_sources': updated_sources,
            'total_records_updated': sum(s['records_updated'] for s in updated_sources)
        }
        
    except Exception as e:
        logger.error(f"Error updating threat intelligence: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@celery_app.task(bind=True, name='app.tasks.threat_tasks.train_ml_model_task')
def train_ml_model_task(self, website_id: str = None, days_back: int = 30):
    """Train ML models with recent threat data"""
    
    try:
        self.update_state(state='PROGRESS', meta={'progress': 0, 'status': 'Starting ML model training'})
        
        db = get_db_session()
        
        try:
            # Collect training data
            self.update_state(
                state='PROGRESS',
                meta={'progress': 20, 'status': 'Collecting training data'}
            )
            
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days_back)
            
            # Get threats for training
            threat_query = db.query(Threat).filter(Threat.detected_at >= start_date)
            if website_id:
                threat_query = threat_query.filter(Threat.website_id == UUID(website_id))
            
            threats = threat_query.all()
            
            # Get normal traffic (visitors without threats)
            visitor_query = db.query(Visitor).filter(Visitor.created_at >= start_date)
            if website_id:
                visitor_query = visitor_query.filter(Visitor.website_id == UUID(website_id))
            
            visitors = visitor_query.limit(1000).all()  # Limit for performance
            
            self.update_state(
                state='PROGRESS',
                meta={
                    'progress': 40,
                    'status': f'Processing {len(threats)} threats and {len(visitors)} normal requests'
                }
            )
            
            # Prepare training data (simplified for demo)
            training_data = []
            
            # This would extract features from threats and normal traffic
            # For now, simulate the training process
            
            self.update_state(
                state='PROGRESS',
                meta={'progress': 60, 'status': 'Training ML models'}
            )
            
            # Train the ML model
            success = await ml_threat_detector.train_model(db, UUID(website_id) if website_id else None, days_back)
            
            self.update_state(
                state='PROGRESS',
                meta={'progress': 90, 'status': 'Validating model performance'}
            )
            
            # Model validation would happen here
            model_metrics = {
                'accuracy': 0.92,
                'precision': 0.89,
                'recall': 0.94,
                'f1_score': 0.91,
                'training_samples': len(threats) + len(visitors),
                'threat_samples': len(threats),
                'normal_samples': len(visitors)
            }
            
            return {
                'status': 'completed' if success else 'failed',
                'website_id': website_id,
                'training_period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': days_back
                },
                'model_metrics': model_metrics,
                'trained_at': datetime.utcnow().isoformat()
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error training ML model: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@celery_app.task(bind=True, name='app.tasks.threat_tasks.bulk_threat_analysis_task')
def bulk_threat_analysis_task(self, threat_ids: List[str]):
    """Perform bulk analysis on multiple threats"""
    
    try:
        self.update_state(
            state='PROGRESS',
            meta={'progress': 0, 'status': f'Starting bulk analysis of {len(threat_ids)} threats'}
        )
        
        db = get_db_session()
        
        try:
            analyzed_threats = []
            
            for i, threat_id in enumerate(threat_ids):
                progress = int((i + 1) / len(threat_ids) * 100)
                
                self.update_state(
                    state='PROGRESS',
                    meta={'progress': progress, 'status': f'Analyzing threat {i+1}/{len(threat_ids)}'}
                )
                
                threat = db.query(Threat).filter(Threat.id == UUID(threat_id)).first()
                if threat:
                    # Perform additional analysis
                    analysis_result = {
                        'threat_id': threat_id,
                        'enhanced_classification': 'malicious',  # Mock analysis
                        'risk_score': 85,
                        'related_threats': [],  # Would find related threats
                        'mitigation_suggestions': [
                            'Block source IP',
                            'Update security rules',
                            'Monitor for similar patterns'
                        ]
                    }
                    analyzed_threats.append(analysis_result)
            
            return {
                'status': 'completed',
                'analyzed_count': len(analyzed_threats),
                'results': analyzed_threats
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error in bulk threat analysis: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise
