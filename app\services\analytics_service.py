"""
Analytics service for business logic
"""

from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException, status

from app.models.website import Website
from app.models.visitor import Visitor
from app.models.threat import Threat
from app.models.scan import Scan
from app.models.user import User
from app.schemas.analytics import (
    DashboardMetrics, VisitorAnalytics, ThreatAnalytics, SecurityAnalytics,
    PerformanceAnalytics, GeographicAnalytics, RealtimeAnalytics,
    AnalyticsQuery, AnalyticsReport, AnalyticsExport
)
from app.services.base_service import BaseService


class AnalyticsService:
    """Analytics service with comprehensive reporting"""
    
    def __init__(self):
        pass
    
    async def get_dashboard_metrics(
        self,
        db: Session,
        *,
        user_id: UUID,
        website_id: Optional[UUID] = None
    ) -> DashboardMetrics:
        """Get main dashboard metrics"""
        # Base query filters
        website_filter = []
        if website_id:
            # Verify website ownership
            website = db.query(Website).filter(
                and_(Website.id == website_id, Website.user_id == user_id)
            ).first()
            if not website:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Website not found or access denied"
                )
            website_filter = [Website.id == website_id]
        else:
            website_filter = [Website.user_id == user_id]
        
        # Date ranges
        today = datetime.utcnow().date()
        yesterday = today - timedelta(days=1)
        today_start = datetime.combine(today, datetime.min.time())
        yesterday_start = datetime.combine(yesterday, datetime.min.time())
        
        # Website metrics
        total_websites = db.query(Website).filter(Website.user_id == user_id).count()
        active_websites = db.query(Website).filter(
            and_(Website.user_id == user_id, Website.is_active == True)
        ).count()
        
        # Visitor metrics
        visitor_query = db.query(Visitor).join(Website).filter(and_(*website_filter))
        total_visitors = visitor_query.count()
        unique_visitors = visitor_query.distinct(Visitor.ip_address).count()
        
        visitors_today = visitor_query.filter(Visitor.created_at >= today_start).count()
        visitors_yesterday = visitor_query.filter(
            and_(
                Visitor.created_at >= yesterday_start,
                Visitor.created_at < today_start
            )
        ).count()
        
        # Threat metrics
        threat_query = db.query(Threat).join(Website).filter(and_(*website_filter))
        total_threats = threat_query.count()
        blocked_threats = threat_query.filter(Threat.is_blocked == True).count()
        
        threats_today = threat_query.filter(Threat.detected_at >= today_start).count()
        threats_yesterday = threat_query.filter(
            and_(
                Threat.detected_at >= yesterday_start,
                Threat.detected_at < today_start
            )
        ).count()
        
        # Scan metrics
        scan_query = db.query(Scan).join(Website).filter(and_(*website_filter))
        total_scans = scan_query.count()
        active_scans = scan_query.filter(Scan.status == "running").count()
        
        # Performance metrics (simplified)
        avg_response_time = 0.5  # TODO: Calculate from actual data
        uptime_percentage = 99.9  # TODO: Calculate from health checks
        security_score = 85.0  # TODO: Calculate from security assessments
        
        # Growth calculations
        visitor_growth = 0.0
        if visitors_yesterday > 0:
            visitor_growth = ((visitors_today - visitors_yesterday) / visitors_yesterday) * 100
        
        threat_growth = 0.0
        if threats_yesterday > 0:
            threat_growth = ((threats_today - threats_yesterday) / threats_yesterday) * 100
        
        # Real-time metrics
        five_minutes_ago = datetime.utcnow() - timedelta(minutes=5)
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        
        active_visitors_now = visitor_query.filter(
            Visitor.last_visit >= five_minutes_ago
        ).count()
        
        threats_last_hour = threat_query.filter(
            Threat.detected_at >= one_hour_ago
        ).count()
        
        return DashboardMetrics(
            total_websites=total_websites,
            active_websites=active_websites,
            total_visitors=total_visitors,
            unique_visitors=unique_visitors,
            total_threats=total_threats,
            blocked_threats=blocked_threats,
            total_scans=total_scans,
            active_scans=active_scans,
            visitors_today=visitors_today,
            visitors_yesterday=visitors_yesterday,
            threats_today=threats_today,
            threats_yesterday=threats_yesterday,
            avg_response_time=avg_response_time,
            uptime_percentage=uptime_percentage,
            security_score=security_score,
            visitor_growth=visitor_growth,
            threat_growth=threat_growth,
            active_visitors_now=active_visitors_now,
            threats_last_hour=threats_last_hour,
            last_updated=datetime.utcnow()
        )
    
    async def get_geographic_analytics(
        self,
        db: Session,
        *,
        user_id: UUID,
        website_id: Optional[UUID] = None,
        days: int = 30
    ) -> GeographicAnalytics:
        """Get geographic analytics data"""
        # Verify access
        website_filter = []
        if website_id:
            website = db.query(Website).filter(
                and_(Website.id == website_id, Website.user_id == user_id)
            ).first()
            if not website:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Website not found or access denied"
                )
            website_filter = [Website.id == website_id]
        else:
            website_filter = [Website.user_id == user_id]
        
        # Date range
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Visitor distribution
        visitor_distribution = db.query(
            Visitor.country,
            Visitor.region,
            Visitor.city,
            func.count(Visitor.id).label('visitor_count'),
            func.avg(Visitor.threat_score).label('avg_threat_score')
        ).join(Website).filter(
            and_(
                *website_filter,
                Visitor.created_at >= start_date,
                Visitor.country.isnot(None)
            )
        ).group_by(Visitor.country, Visitor.region, Visitor.city).all()
        
        # Threat distribution
        threat_distribution = db.query(
            Threat.source_country,
            func.count(Threat.id).label('threat_count'),
            func.avg(Threat.confidence_score).label('avg_confidence')
        ).join(Website).filter(
            and_(
                *website_filter,
                Threat.detected_at >= start_date,
                Threat.source_country.isnot(None)
            )
        ).group_by(Threat.source_country).all()
        
        # Top countries
        top_countries = db.query(
            Visitor.country,
            func.count(Visitor.id).label('count')
        ).join(Website).filter(
            and_(
                *website_filter,
                Visitor.created_at >= start_date,
                Visitor.country.isnot(None)
            )
        ).group_by(Visitor.country).order_by(desc('count')).limit(10).all()
        
        # Convert to response format
        visitor_dist = [
            {
                "country": v.country,
                "region": v.region,
                "city": v.city,
                "visitor_count": v.visitor_count,
                "avg_threat_score": float(v.avg_threat_score or 0)
            }
            for v in visitor_distribution
        ]
        
        threat_dist = [
            {
                "country": t.source_country,
                "threat_count": t.threat_count,
                "avg_confidence": float(t.avg_confidence or 0)
            }
            for t in threat_distribution
        ]
        
        top_countries_list = [
            {"country": c.country, "count": c.count}
            for c in top_countries
        ]
        
        # Get coordinates for mapping (simplified)
        visitor_coordinates = []
        threat_coordinates = []
        
        return GeographicAnalytics(
            visitor_distribution=visitor_dist,
            threat_distribution=threat_dist,
            performance_by_location=[],  # TODO: Implement
            risk_by_location=[],  # TODO: Implement
            top_countries=top_countries_list,
            top_regions=[],  # TODO: Implement
            top_cities=[],  # TODO: Implement
            visitor_coordinates=visitor_coordinates,
            threat_coordinates=threat_coordinates
        )
    
    async def get_realtime_analytics(
        self,
        db: Session,
        *,
        user_id: UUID,
        website_id: Optional[UUID] = None
    ) -> RealtimeAnalytics:
        """Get real-time analytics data"""
        # Verify access
        website_filter = []
        if website_id:
            website = db.query(Website).filter(
                and_(Website.id == website_id, Website.user_id == user_id)
            ).first()
            if not website:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Website not found or access denied"
                )
            website_filter = [Website.id == website_id]
        else:
            website_filter = [Website.user_id == user_id]
        
        # Time ranges
        five_minutes_ago = datetime.utcnow() - timedelta(minutes=5)
        one_minute_ago = datetime.utcnow() - timedelta(minutes=1)
        
        # Current activity
        active_visitors = db.query(Visitor).join(Website).filter(
            and_(*website_filter, Visitor.last_visit >= five_minutes_ago)
        ).count()
        
        active_sessions = active_visitors  # Simplified
        
        current_threats = db.query(Threat).join(Website).filter(
            and_(
                *website_filter,
                Threat.detected_at >= five_minutes_ago,
                Threat.is_false_positive == False
            )
        ).count()
        
        active_scans = db.query(Scan).join(Website).filter(
            and_(*website_filter, Scan.status == "running")
        ).count()
        
        # Recent activity
        recent_visitors = db.query(Visitor).join(Website).filter(
            and_(*website_filter)
        ).order_by(desc(Visitor.last_visit)).limit(5).all()
        
        recent_threats = db.query(Threat).join(Website).filter(
            and_(*website_filter, Threat.is_false_positive == False)
        ).order_by(desc(Threat.detected_at)).limit(5).all()
        
        # Live metrics
        requests_per_minute = db.query(Visitor).join(Website).filter(
            and_(*website_filter, Visitor.last_visit >= one_minute_ago)
        ).count()
        
        threats_per_minute = db.query(Threat).join(Website).filter(
            and_(*website_filter, Threat.detected_at >= one_minute_ago)
        ).count()
        
        return RealtimeAnalytics(
            timestamp=datetime.utcnow(),
            active_visitors=active_visitors,
            active_sessions=active_sessions,
            current_threats=current_threats,
            active_scans=active_scans,
            recent_visitors=[
                {
                    "id": str(v.id),
                    "ip_address": str(v.ip_address),
                    "country": v.country,
                    "last_visit": v.last_visit.isoformat() if v.last_visit else None
                }
                for v in recent_visitors
            ],
            recent_threats=[
                {
                    "id": str(t.id),
                    "threat_type": t.threat_type,
                    "severity": t.severity,
                    "detected_at": t.detected_at.isoformat() if t.detected_at else None
                }
                for t in recent_threats
            ],
            recent_page_views=[],  # TODO: Implement page view tracking
            requests_per_minute=requests_per_minute,
            threats_per_minute=threats_per_minute,
            avg_response_time_live=0.5,  # TODO: Calculate from real data
            live_visitor_map=[],  # TODO: Implement
            live_threat_map=[],  # TODO: Implement
            system_health={
                "database": "healthy",
                "cache": "healthy",
                "api": "healthy"
            },
            api_status={
                "websites": "operational",
                "visitors": "operational",
                "threats": "operational",
                "scans": "operational"
            }
        )
    
    async def generate_report(
        self,
        db: Session,
        *,
        user_id: UUID,
        website_id: Optional[UUID] = None,
        report_type: str = "comprehensive",
        days: int = 30
    ) -> AnalyticsReport:
        """Generate comprehensive analytics report"""
        # This would be a comprehensive report generation
        # For now, return a basic structure
        
        report_id = UUID("00000000-0000-0000-0000-000000000000")  # Generate proper UUID
        start_date = datetime.utcnow() - timedelta(days=days)
        end_date = datetime.utcnow()
        
        # Get basic metrics for the report
        dashboard_metrics = await self.get_dashboard_metrics(db, user_id=user_id, website_id=website_id)
        
        return AnalyticsReport(
            report_id=report_id,
            website_id=website_id,
            report_type=report_type,
            time_range="custom",
            start_date=start_date,
            end_date=end_date,
            executive_summary={
                "total_visitors": dashboard_metrics.total_visitors,
                "total_threats": dashboard_metrics.total_threats,
                "security_score": dashboard_metrics.security_score,
                "uptime": dashboard_metrics.uptime_percentage
            },
            visitor_analytics=VisitorAnalytics(
                total_visitors=dashboard_metrics.total_visitors,
                unique_visitors=dashboard_metrics.unique_visitors,
                returning_visitors=0,
                new_visitors=dashboard_metrics.total_visitors,
                bounce_rate=0.0,
                avg_session_duration=0.0,
                page_views=0,
                avg_pages_per_session=0.0,
                hourly_visitors=[],
                daily_visitors=[],
                top_countries=[],
                top_regions=[],
                top_cities=[],
                device_breakdown={},
                browser_breakdown={},
                os_breakdown={},
                referrer_breakdown={},
                search_engines={},
                risk_distribution={},
                vpn_usage=0,
                proxy_usage=0,
                tor_usage=0
            ),
            threat_analytics=ThreatAnalytics(
                total_threats=dashboard_metrics.total_threats,
                blocked_threats=dashboard_metrics.blocked_threats,
                false_positives=0,
                avg_confidence_score=0.0,
                critical_threats=0,
                high_threats=0,
                medium_threats=0,
                low_threats=0,
                threats_by_severity={},
                threats_by_type={},
                threats_by_country=[],
                top_threat_sources=[],
                top_attack_vectors=[],
                peak_attack_times=[],
                blocking_effectiveness=0.0,
                response_time=0.0,
                threat_trend="stable",
                severity_trend={}
            ),
            security_analytics=SecurityAnalytics(
                overall_security_score=dashboard_metrics.security_score,
                vulnerability_count=0,
                fixed_vulnerabilities=0,
                open_vulnerabilities=0,
                vulnerabilities_by_severity={},
                vulnerabilities_by_type={},
                compliance_scores={},
                ssl_certificates={},
                ssl_grade_distribution={},
                security_headers_compliance={},
                last_scan_results={},
                scan_frequency={},
                top_recommendations=[],
                priority_fixes=[]
            ),
            performance_analytics=PerformanceAnalytics(
                avg_response_time=dashboard_metrics.avg_response_time,
                median_response_time=dashboard_metrics.avg_response_time,
                p95_response_time=dashboard_metrics.avg_response_time * 1.5,
                p99_response_time=dashboard_metrics.avg_response_time * 2,
                uptime_percentage=dashboard_metrics.uptime_percentage,
                downtime_incidents=0,
                mttr=0.0,
                page_load_times={},
                slowest_pages=[],
                hourly_performance=[],
                daily_performance=[],
                error_rate=0.0,
                status_code_distribution={},
                bandwidth_usage=0.0,
                request_volume=0,
                performance_by_region=[]
            ),
            geographic_analytics=GeographicAnalytics(
                visitor_distribution=[],
                threat_distribution=[],
                performance_by_location=[],
                risk_by_location=[],
                top_countries=[],
                top_regions=[],
                top_cities=[],
                visitor_coordinates=[],
                threat_coordinates=[]
            ),
            recommendations=[
                "Implement regular security scans",
                "Monitor threat patterns",
                "Optimize website performance",
                "Review security configurations"
            ],
            action_items=[
                {"priority": "high", "item": "Address critical security vulnerabilities"},
                {"priority": "medium", "item": "Improve response time performance"},
                {"priority": "low", "item": "Enhance monitoring coverage"}
            ],
            generated_at=datetime.utcnow(),
            generated_by=user_id,
            format="json"
        )
