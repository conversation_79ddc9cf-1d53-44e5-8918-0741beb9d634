"""
Basic tests for the main application
"""

import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)


def test_root_endpoint():
    """Test the root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "ProSecurity Monitor API"
    assert data["version"] == "1.0.0"
    assert "status" in data


def test_health_check():
    """Test the health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"


def test_api_docs_available():
    """Test that API documentation is available in development"""
    response = client.get("/docs")
    # Should be available in development mode
    assert response.status_code in [200, 404]  # 404 if in production mode
