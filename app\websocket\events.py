"""
WebSocket event types and models
"""

from datetime import datetime
from typing import Any, Dict, Optional, List
from uuid import UUID
from pydantic import BaseModel
from enum import Enum


class EventType(str, Enum):
    """WebSocket event types"""
    # Visitor events
    VISITOR_CONNECTED = "visitor_connected"
    VISITOR_DISCONNECTED = "visitor_disconnected"
    VISITOR_PAGE_VIEW = "visitor_page_view"
    VISITOR_BLOCKED = "visitor_blocked"
    
    # Threat events
    THREAT_DETECTED = "threat_detected"
    THREAT_BLOCKED = "threat_blocked"
    THREAT_RESOLVED = "threat_resolved"
    HIGH_RISK_ACTIVITY = "high_risk_activity"
    
    # Scan events
    SCAN_STARTED = "scan_started"
    SCAN_PROGRESS = "scan_progress"
    SCAN_COMPLETED = "scan_completed"
    SCAN_FAILED = "scan_failed"
    
    # System events
    SYSTEM_ALERT = "system_alert"
    WEBSITE_DOWN = "website_down"
    WEBSITE_UP = "website_up"
    SSL_EXPIRING = "ssl_expiring"
    
    # Analytics events
    ANALYTICS_UPDATE = "analytics_update"
    DASHBOARD_REFRESH = "dashboard_refresh"
    REPORT_GENERATED = "report_generated"
    
    # Connection events
    CONNECTION_ESTABLISHED = "connection_established"
    CONNECTION_ERROR = "connection_error"
    HEARTBEAT = "heartbeat"


class WebSocketEvent(BaseModel):
    """WebSocket event model"""
    event_type: EventType
    timestamp: datetime
    data: Dict[str, Any]
    website_id: Optional[UUID] = None
    user_id: Optional[UUID] = None
    event_id: Optional[str] = None
    priority: str = "normal"  # low, normal, high, critical
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class VisitorEvent(WebSocketEvent):
    """Visitor-specific event"""
    visitor_id: UUID
    ip_address: str
    country: Optional[str] = None
    device_type: Optional[str] = None
    
    @classmethod
    def create(
        cls,
        event_type: EventType,
        visitor_id: UUID,
        ip_address: str,
        website_id: UUID,
        **kwargs
    ):
        return cls(
            event_type=event_type,
            timestamp=datetime.utcnow(),
            visitor_id=visitor_id,
            ip_address=ip_address,
            website_id=website_id,
            data=kwargs
        )


class ThreatEvent(WebSocketEvent):
    """Threat-specific event"""
    threat_id: UUID
    threat_type: str
    severity: str
    confidence_score: int
    source_ip: Optional[str] = None
    
    @classmethod
    def create(
        cls,
        event_type: EventType,
        threat_id: UUID,
        threat_type: str,
        severity: str,
        confidence_score: int,
        website_id: UUID,
        **kwargs
    ):
        return cls(
            event_type=event_type,
            timestamp=datetime.utcnow(),
            threat_id=threat_id,
            threat_type=threat_type,
            severity=severity,
            confidence_score=confidence_score,
            website_id=website_id,
            priority="high" if severity in ["high", "critical"] else "normal",
            data=kwargs
        )


class ScanEvent(WebSocketEvent):
    """Scan-specific event"""
    scan_id: UUID
    scan_type: str
    progress: int = 0
    status: str = "running"
    
    @classmethod
    def create(
        cls,
        event_type: EventType,
        scan_id: UUID,
        scan_type: str,
        website_id: UUID,
        progress: int = 0,
        **kwargs
    ):
        return cls(
            event_type=event_type,
            timestamp=datetime.utcnow(),
            scan_id=scan_id,
            scan_type=scan_type,
            progress=progress,
            website_id=website_id,
            data=kwargs
        )


class SystemEvent(WebSocketEvent):
    """System-specific event"""
    alert_level: str = "info"  # info, warning, error, critical
    message: str
    component: Optional[str] = None
    
    @classmethod
    def create(
        cls,
        event_type: EventType,
        message: str,
        alert_level: str = "info",
        website_id: Optional[UUID] = None,
        **kwargs
    ):
        return cls(
            event_type=event_type,
            timestamp=datetime.utcnow(),
            message=message,
            alert_level=alert_level,
            website_id=website_id,
            priority="critical" if alert_level == "critical" else "normal",
            data=kwargs
        )


class AnalyticsEvent(WebSocketEvent):
    """Analytics-specific event"""
    metric_type: str
    metric_value: Any
    change_percentage: Optional[float] = None
    
    @classmethod
    def create(
        cls,
        event_type: EventType,
        metric_type: str,
        metric_value: Any,
        website_id: Optional[UUID] = None,
        **kwargs
    ):
        return cls(
            event_type=event_type,
            timestamp=datetime.utcnow(),
            metric_type=metric_type,
            metric_value=metric_value,
            website_id=website_id,
            data=kwargs
        )


class ConnectionEvent(WebSocketEvent):
    """Connection-specific event"""
    connection_id: str
    client_info: Dict[str, Any]
    
    @classmethod
    def create(
        cls,
        event_type: EventType,
        connection_id: str,
        client_info: Dict[str, Any],
        user_id: UUID,
        **kwargs
    ):
        return cls(
            event_type=event_type,
            timestamp=datetime.utcnow(),
            connection_id=connection_id,
            client_info=client_info,
            user_id=user_id,
            data=kwargs
        )


class EventFilter(BaseModel):
    """Event filtering for subscriptions"""
    event_types: Optional[List[EventType]] = None
    website_ids: Optional[List[UUID]] = None
    priority_levels: Optional[List[str]] = None
    include_historical: bool = False
    max_events: int = 100


class EventSubscription(BaseModel):
    """WebSocket subscription configuration"""
    subscription_id: str
    user_id: UUID
    filters: EventFilter
    created_at: datetime = datetime.utcnow()
    last_activity: datetime = datetime.utcnow()
    
    def matches_event(self, event: WebSocketEvent) -> bool:
        """Check if event matches subscription filters"""
        # Check event types
        if self.filters.event_types and event.event_type not in self.filters.event_types:
            return False
        
        # Check website IDs
        if self.filters.website_ids and event.website_id not in self.filters.website_ids:
            return False
        
        # Check priority levels
        if self.filters.priority_levels and event.priority not in self.filters.priority_levels:
            return False
        
        return True


class HeartbeatEvent(WebSocketEvent):
    """Heartbeat event for connection health"""
    server_time: datetime
    connection_count: int
    
    @classmethod
    def create(cls, connection_count: int):
        return cls(
            event_type=EventType.HEARTBEAT,
            timestamp=datetime.utcnow(),
            server_time=datetime.utcnow(),
            connection_count=connection_count,
            data={}
        )
