"""
Visitor schemas for request/response models
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field, IPvAnyAddress
from enum import Enum


class RiskLevel(str, Enum):
    """Risk level enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class DeviceType(str, Enum):
    """Device type enumeration"""
    DESKTOP = "desktop"
    MOBILE = "mobile"
    TABLET = "tablet"
    BOT = "bot"
    UNKNOWN = "unknown"


class VisitorCreate(BaseModel):
    """Schema for creating a new visitor record"""
    website_id: UUID
    ip_address: IPvAnyAddress
    mac_address: Optional[str] = Field(None, max_length=17)
    user_agent: Optional[str] = Field(None, max_length=1000)
    
    # Geographic information
    country: Optional[str] = Field(None, max_length=2)
    region: Optional[str] = Field(None, max_length=100)
    city: Optional[str] = Field(None, max_length=100)
    latitude: Optional[str] = Field(None, max_length=20)
    longitude: Optional[str] = Field(None, max_length=20)
    timezone: Optional[str] = Field(None, max_length=50)
    
    # ISP and network details
    isp: Optional[str] = Field(None, max_length=255)
    organization: Optional[str] = Field(None, max_length=255)
    asn: Optional[str] = Field(None, max_length=20)
    
    # Device information
    device_type: Optional[DeviceType] = DeviceType.UNKNOWN
    browser: Optional[str] = Field(None, max_length=100)
    browser_version: Optional[str] = Field(None, max_length=50)
    os: Optional[str] = Field(None, max_length=100)
    os_version: Optional[str] = Field(None, max_length=50)
    
    # Security flags
    is_vpn: bool = False
    is_proxy: bool = False
    is_tor: bool = False
    
    # Additional metadata
    visitor_metadata: Optional[Dict[str, Any]] = None


class VisitorUpdate(BaseModel):
    """Schema for updating visitor information"""
    total_visits: Optional[int] = Field(None, ge=0)
    total_pages: Optional[int] = Field(None, ge=0)
    total_time_spent: Optional[int] = Field(None, ge=0)
    risk_level: Optional[RiskLevel] = None
    threat_score: Optional[int] = Field(None, ge=0, le=100)
    is_blocked: Optional[bool] = None
    visitor_metadata: Optional[Dict[str, Any]] = None


class VisitorResponse(BaseModel):
    """Schema for visitor response"""
    id: UUID
    website_id: UUID
    ip_address: str
    mac_address: Optional[str]
    user_agent: Optional[str]
    
    # Geographic information
    country: Optional[str]
    region: Optional[str]
    city: Optional[str]
    latitude: Optional[str]
    longitude: Optional[str]
    timezone: Optional[str]
    
    # ISP and network details
    isp: Optional[str]
    organization: Optional[str]
    asn: Optional[str]
    
    # Device information
    device_type: Optional[str]
    browser: Optional[str]
    browser_version: Optional[str]
    os: Optional[str]
    os_version: Optional[str]
    
    # Visit statistics
    first_visit: datetime
    last_visit: datetime
    total_visits: int
    total_pages: int
    total_time_spent: int
    
    # Security assessment
    is_blocked: bool
    risk_level: str
    threat_score: int
    is_vpn: bool
    is_proxy: bool
    is_tor: bool
    
    # Metadata
    visitor_metadata: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class VisitorDetailResponse(VisitorResponse):
    """Detailed visitor response with additional information"""
    threat_count: int = 0
    pages_visited: List[str] = []
    session_duration: int = 0
    bounce_rate: float = 0.0
    conversion_events: int = 0


class VisitorListResponse(BaseModel):
    """Response for visitor listing with pagination"""
    visitors: List[VisitorResponse]
    total: int
    page: int
    per_page: int
    pages: int


class VisitorAnalytics(BaseModel):
    """Visitor analytics data"""
    total_visitors: int
    unique_visitors: int
    returning_visitors: int
    new_visitors: int
    avg_session_duration: float
    bounce_rate: float
    top_countries: List[Dict[str, Any]]
    top_devices: List[Dict[str, Any]]
    top_browsers: List[Dict[str, Any]]
    hourly_distribution: List[Dict[str, int]]
    daily_distribution: List[Dict[str, int]]


class VisitorGeographicData(BaseModel):
    """Geographic distribution of visitors"""
    country_distribution: List[Dict[str, Any]]
    region_distribution: List[Dict[str, Any]]
    city_distribution: List[Dict[str, Any]]
    coordinates: List[Dict[str, float]]
    risk_by_location: List[Dict[str, Any]]


class VisitorRealTimeData(BaseModel):
    """Real-time visitor data"""
    active_visitors: int
    recent_visitors: List[VisitorResponse]
    geographic_activity: List[Dict[str, Any]]
    device_breakdown: Dict[str, int]
    threat_alerts: List[Dict[str, Any]]
    timestamp: datetime


class VisitorFilter(BaseModel):
    """Visitor filtering options"""
    country: Optional[str] = None
    region: Optional[str] = None
    city: Optional[str] = None
    device_type: Optional[DeviceType] = None
    risk_level: Optional[RiskLevel] = None
    is_blocked: Optional[bool] = None
    is_vpn: Optional[bool] = None
    is_proxy: Optional[bool] = None
    is_tor: Optional[bool] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    min_visits: Optional[int] = None
    max_visits: Optional[int] = None


class VisitorBulkAction(BaseModel):
    """Bulk action on visitors"""
    visitor_ids: List[UUID]
    action: str = Field(..., description="Action: block, unblock, delete, update_risk")
    parameters: Optional[Dict[str, Any]] = None
