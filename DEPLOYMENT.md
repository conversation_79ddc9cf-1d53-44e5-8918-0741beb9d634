# ProSecurity Monitor - Railway Deployment Guide

This guide walks you through deploying the ProSecurity Monitor backend to Railway with full CI/CD integration.

## 🚂 Railway Deployment Steps

### 1. Prerequisites

- GitHub account with this repository
- Railway account (sign up at [railway.app](https://railway.app))
- Railway CLI installed locally

### 2. Install Railway CLI

```bash
# Install Railway CLI
npm install -g @railway/cli

# Or using curl
curl -fsSL https://railway.app/install.sh | sh
```

### 3. Setup Railway Project

```bash
# Login to Railway
railway login

# Initialize project in the backend directory
cd Prosecurity-monitor-backend
railway init

# Link to existing project (if you already created one)
railway link [project-id]
```

### 4. Add Required Services

```bash
# Add PostgreSQL database
railway add postgresql

# Add Redis for caching
railway add redis

# Deploy the application
railway up
```

### 5. Configure Environment Variables

In your Railway dashboard, add these environment variables:

#### Required Variables
```
SECRET_KEY=your-super-secret-jwt-key-here
ENVIRONMENT=production
```

#### Optional Variables
```
GEOLOCATION_API_KEY=your-geolocation-api-key
THREAT_INTELLIGENCE_API_KEY=your-threat-intel-api-key
SENTRY_DSN=your-sentry-dsn-for-error-tracking
ALLOWED_ORIGINS=https://your-frontend-domain.com,https://prosecurity-monitor.vercel.app
```

### 6. Setup GitHub CI/CD

1. **Add GitHub Secrets**
   
   Go to your GitHub repository → Settings → Secrets and variables → Actions
   
   Add these secrets:
   ```
   RAILWAY_TOKEN=your-railway-api-token
   RAILWAY_SERVICE_ID=your-railway-service-id
   ```

2. **Get Railway Token**
   ```bash
   railway login
   railway auth
   ```

3. **Get Service ID**
   ```bash
   railway status
   ```

### 7. Automatic Deployment

Once configured, every push to `main` branch will automatically:
- Run tests
- Security scans
- Deploy to Railway
- Update your live application

## 🔧 Manual Deployment

If you prefer manual deployment:

```bash
# Make sure you're in the backend directory
cd Prosecurity-monitor-backend

# Deploy to Railway
railway up

# Check deployment status
railway status

# View logs
railway logs
```

## 🌐 Custom Domain (Optional)

1. Go to your Railway project dashboard
2. Click on your service
3. Go to Settings → Domains
4. Add your custom domain
5. Update DNS records as instructed

## 📊 Monitoring Your Deployment

### Health Checks
- Health endpoint: `https://your-app.railway.app/health`
- API docs: `https://your-app.railway.app/docs`

### Logs
```bash
# View real-time logs
railway logs --follow

# View specific service logs
railway logs --service backend
```

### Database Management
```bash
# Connect to PostgreSQL
railway connect postgresql

# Run database migrations (when implemented)
railway run alembic upgrade head
```

## 🔒 Security Considerations

### Environment Variables
- Never commit `.env` files
- Use Railway's environment variable management
- Rotate secrets regularly

### Database Security
- Railway PostgreSQL includes SSL by default
- Use private networking when possible
- Regular backups are handled by Railway

### Application Security
- HTTPS is enforced by Railway
- Security headers are added by middleware
- Rate limiting is implemented

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Check build logs
   railway logs --deployment [deployment-id]
   
   # Rebuild
   railway up --detach
   ```

2. **Database Connection Issues**
   ```bash
   # Check database status
   railway status
   
   # Restart database service
   railway restart --service postgresql
   ```

3. **Environment Variables Not Loading**
   - Verify variables are set in Railway dashboard
   - Check variable names match exactly
   - Restart the service after adding variables

### Performance Optimization

1. **Database Optimization**
   - Use connection pooling (already configured)
   - Monitor query performance
   - Add indexes for frequently queried fields

2. **Caching**
   - Redis is configured for caching
   - Implement caching for expensive operations
   - Use Redis for session storage

3. **Monitoring**
   - Set up Sentry for error tracking
   - Monitor response times
   - Set up alerts for critical issues

## 📈 Scaling

Railway automatically handles:
- Horizontal scaling based on traffic
- Load balancing
- SSL certificate management
- CDN for static assets

For high-traffic scenarios:
- Consider upgrading to Railway Pro
- Implement database read replicas
- Use Redis clustering
- Add monitoring and alerting

## 🆘 Support

- Railway Documentation: [docs.railway.app](https://docs.railway.app)
- Railway Discord: [discord.gg/railway](https://discord.gg/railway)
- GitHub Issues: Create issues in this repository

## 📝 Next Steps

After successful deployment:

1. **Test all endpoints** using the API documentation
2. **Set up monitoring** with Sentry or similar
3. **Configure alerts** for critical issues
4. **Set up backup strategies** for important data
5. **Document your API** for frontend integration
6. **Implement rate limiting** for production use
7. **Set up logging aggregation** for better debugging

Your ProSecurity Monitor backend is now live and ready to serve your frontend application!
